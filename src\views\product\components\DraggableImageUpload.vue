<template>
  <div class="draggable-image-upload-container" :class="{
    'drag-disabled': !enableDrag || disabled,
    'disabled': disabled
  }">
    <div class="upload-ocr-layout">
      <!-- 左侧：图片上传区域 -->
      <div class="left-section">
        <!-- 拖拽区域 -->
        <div class="upload-section wrap">
          <!-- 统一的图片网格布局 -->
          <div class="unified-image-grid">
            <!-- 拖拽模式 -->
            <draggable v-if="enableDrag && !disabled" v-model="list" :group="dragGroupConfig" :disabled="disabled"
              @change="handleDragChange" @add="handleDragAdd" @remove="handleDragRemove" class="draggable-image-list"
              :class="{ 'empty-container': isEmptyContainer }" tag="div" :animation="200" ghost-class="sortable-ghost"
              chosen-class="sortable-chosen" drag-class="sortable-drag" :force-fallback="false" :fallback-tolerance="0"
              :empty-insert-threshold="50">
              <!-- 图片项 -->
              <div v-for="(file, index) in list" :key="getFileKey(file, index)"
                class="image-item-container draggable-item">
                <ImageItemContent :file="file" :enableOCR="enableOCR" :disabled="disabled" :preview="preview"
                  @preview="handlePreview" @remove="handleRemove" @left="handleLeft" @right="handleRight"
                  @extractOCR="extractTextFromImage" :ocrResult="getOcrResult(file)"
                  :isImageReady="isImageReady(file)" />
              </div>

              <!-- 空容器拖拽占位元素 - 确保 vuedraggable 有可靠的拖拽目标 -->
              <div v-if="shouldShowPlaceholder" class="drag-placeholder" :key="'placeholder-' + dragGroup"
                @click="triggerUpload">
                <div class="placeholder-content">
                  点击上传或拖拽图片到此处
                </div>
              </div>
            </draggable>

            <!-- 非拖拽模式 -->
            <div v-else class="static-image-list">
              <div v-for="(file, index) in list" :key="getFileKey(file, index)" class="image-item-container">
                <ImageItemContent :file="file" :enableOCR="enableOCR" :disabled="disabled" :preview="preview"
                  @preview="handlePreview" @remove="handleRemove" @left="handleLeft" @right="handleRight"
                  @extractOCR="extractTextFromImage" :ocrResult="getOcrResult(file)"
                  :isImageReady="isImageReady(file)" />
              </div>
            </div>

            <!-- 上传按钮 - 只在有内容时显示 -->
            <div v-if="!disabled && (!limit || list.length < limit) && !isEmptyContainer"
              class="upload-button-container" :class="{ 'drag-enabled': enableDrag && !disabled }">
              <el-upload :action="uploadUrl" list-type="picture-card" multiple :disabled="disabled" :limit="limit"
                :on-exceed="handleExceed" :before-upload="beforeUpload" :on-success="handleSuccess"
                :on-error="handleError" :file-list="[]" :auto-upload="true" :show-file-list="false"
                :drag="enableDrag && !disabled" class="inline-upload">
                <div slot="default" class="upload-content">
                  <i class="el-icon-plus upload-icon"></i>
                  <div v-if="enableDrag && !disabled && list.length === 0" class="upload-hint">
                    <p class="click-hint">点击上传</p>
                    <p class="drag-hint">或拖拽到此处</p>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：OCR结果展示区域 -->
      <div class="right-section" v-if="enableOCR && hasOcrResults">
        <div class="ocr-results-section">
          <div class="ocr-results-header">
            <div class="ocr-results-title">提取结果：</div>
            <div class="ocr-results-controls">
              <span class="expand-toggle-btn" @click="toggleExpanded" :title="isExpanded ? '收起' : '展开'">
                {{ isExpanded ? '收起' : '展开' }}
              </span>
            </div>
          </div>

          <div class="ocr-results-content" :class="{ 'expanded': isExpanded }" ref="ocrResultsContent">
            <div v-for="(file, index) in list" :key="file.uid || file.mediaUrl || file.url">
              <div v-if="getOcrResult(file).extracted && getOcrResult(file).fields.length > 0" class="ocr-result-group">
                <div class="ocr-result-image-info">
                  <span class="image-name">{{ '图片' + (index + 1) || file.name || file.mediaName }}</span>
                  <span class="delete-result-btn" @click="deleteOcrResult(file)" title="删除识别结果">
                    <i class="el-icon-delete"></i>
                  </span>
                </div>
                <div class="ocr-result-fields">
                  <div v-for="field in getOcrResult(file).fields" :key="field.id" class="ocr-result-field">
                    <div class="field-content">
                      <span class="field-label">{{ field.label }}：</span>
                      <span class="field-value">{{ field.value }}</span>
                    </div>
                    <div class="field-copy">
                      <span class="copy-btn" @click="copyFieldValue(field.value)" title="复制">
                        <i class="el-icon-document-copy"></i>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图片预览组件 -->
      <imagePreviewWithOCR :on-close="closeImageViewer" v-if="imgPreview" :url-list="imgPreviewList"
        :initialIndex="initialIndex" :enable-o-c-r="enableOCR" :ocr-results="ocrResults"
        :extract-text-from-image="extractTextFromImageInPreview" :copy-info="copyInfo"></imagePreviewWithOCR>
    </div>
  </div>
</template>

<script>
import draggable from "vuedraggable";
import ImageItemContent from "./ImageItemContent.vue";
import imagePreviewWithOCR from "@/components/common/preview/imagePreviewWithOCR";
import imageConversion from "image-conversion";
import request from "@/utils/request";
import { getOcrProxyPath } from "@/config/ocr";

// 扩展数组原型方法
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};

export default {
  name: "DraggableImageUpload",
  components: {
    draggable,
    ImageItemContent,
    imagePreviewWithOCR,
  },
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    uploadUrl: {
      type: String,
      default: "",
    },
    limit: {
      type: Number,
      default: 10,
    },
    minWidth: {
      type: Number,
      default: 0,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
    enableOCR: {
      type: Boolean,
      default: true,
    },
    enableDrag: {
      type: Boolean,
      default: true,
    },
    dragGroup: {
      type: String,
      default: 'images',
    },
    dragPutValidator: {
      type: Function,
      default: () => true,
    },
    ocrApiUrl: {
      type: String,
      default: "",
    },
    // 全局 OCR 数据管理器
    globalOcrManager: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      initialIndex: 0,
      list: [],
      fileNameList: [],
      imgPreview: false,
      imgPreviewList: [],
      ocrResults: {},
      isExpanded: false,
      // 标志位：是否正在从上传操作更新数据
      isUpdatingFromUpload: false,
    };
  },
  computed: {
    hasOcrResults() {
      return Object.keys(this.ocrResults).some(key =>
        this.ocrResults[key].extracted && this.ocrResults[key].fields.length > 0
      );
    },
    dragGroupConfig() {
      if (!this.enableDrag) {
        return false;
      }

      // 确保空容器也能接受拖拽
      const putValidator = this.dragPutValidator;
      return {
        name: this.dragGroup,
        pull: true,
        put: putValidator || true, // 如果没有验证器，默认允许放入
      };
    },

    // 计算属性：检测是否为空容器
    isEmptyContainer() {
      return this.list.length === 0;
    },

    // 计算属性：检测是否应该显示拖拽占位元素
    shouldShowPlaceholder() {
      return this.isEmptyContainer && this.enableDrag && !this.disabled;
    },
  },
  watch: {
    fileList: {
      handler(newVal) {
        // 如果是上传操作导致的变化，不要覆盖内部数据
        if (this.isUpdatingFromUpload) {
          return;
        }

        // 更新内部数据
        this.list = newVal ? [...newVal] : [];
        this.cleanupOcrResults(this.list);

        // 刷新 OCR 数据以确保显示正确
        if (this.globalOcrManager && this.list.length > 0) {
          this.$nextTick(() => {
            this.refreshAllOcrData();
          });
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听容器状态变化，确保空容器状态切换及时
    isEmptyContainer: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          console.log('[ContainerState] Empty container state changed:', {
            dragGroup: this.dragGroup,
            isNowEmpty: newVal,
            wasEmpty: oldVal,
            listLength: this.list.length,
            shouldShowPlaceholder: this.shouldShowPlaceholder
          });

          // 如果容器变为空，强制更新视图
          if (newVal && this.enableDrag && !this.disabled) {
            this.$nextTick(() => {
              console.log('[ContainerState] Forcing update for empty container');
              this.$forceUpdate();
            });
          }
        }
      },
      immediate: true,
    },

    // 监听全局 OCR 管理器的变化
    globalOcrManager: {
      handler(newManager, oldManager) {
        if (newManager !== oldManager) {
          console.log('[OCR] Global OCR manager changed:', {
            dragGroup: this.dragGroup,
            hasNewManager: !!newManager,
            hasOldManager: !!oldManager
          });

          // 如果有新的管理器，刷新所有 OCR 数据
          if (newManager && this.list.length > 0) {
            this.$nextTick(() => {
              this.refreshAllOcrData();
            });
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    console.log('[DraggableImageUpload] Component mounted:', {
      dragGroup: this.dragGroup,
      enableDrag: this.enableDrag,
      disabled: this.disabled,
      listLength: this.list.length,
      dragGroupConfig: this.dragGroupConfig,
      hasGlobalOcrManager: !!this.globalOcrManager
    });

    // 挂载后刷新所有 OCR 数据
    if (this.globalOcrManager && this.list.length > 0) {
      this.$nextTick(() => {
        this.refreshAllOcrData();
      });
    }
  },
  methods: {
    handleDragChange(evt) {
      console.log('[DragChange] Event triggered:', {
        dragGroup: this.dragGroup,
        eventType: evt.type || 'unknown',
        listLength: this.list.length,
        event: evt
      });

      this.isUpdatingFromUpload = true;
      this.$emit('dragChange', evt);
      this.$emit('change', this.list);
      this.$nextTick(() => {
        this.isUpdatingFromUpload = false;
      });
    },

    handleDragAdd(evt) {
      console.log('[DragAdd] Event triggered:', {
        dragGroup: this.dragGroup,
        newIndex: evt.newIndex,
        listLength: this.list.length,
        wasEmpty: this.list.length === 1,
        addedItem: evt.item
      });

      this.isUpdatingFromUpload = true;

      // 检查新添加的文件是否有 OCR 数据需要迁移
      if (evt.item && evt.item.__vue__) {
        const draggedFile = evt.item.__vue__.file || evt.item.__vue__.$attrs.file;
        if (draggedFile) {
          console.log('[DragAdd] Checking OCR data for dragged file:', draggedFile.name);

          // 如果使用全局 OCR 管理器，强制刷新 OCR 数据
          if (this.globalOcrManager) {
            const globalOcrData = this.globalOcrManager.getGlobalOcrData(draggedFile);
            if (globalOcrData && globalOcrData.extracted) {
              console.log('[DragAdd] OCR data found in global manager for file:', draggedFile.name);

              // 强制刷新本地 OCR 数据以确保 UI 更新
              this.$nextTick(() => {
                this.refreshOcrDataForFile(draggedFile);
              });
            }
          }
        }
      }

      // 立即触发状态更新
      this.$emit('dragChange', evt);
      this.$emit('change', this.list);

      // 如果容器从空变为有内容，强制更新视图
      if (this.list.length === 1) {
        console.log('[DragAdd] Container is no longer empty, forcing view update');
        this.$forceUpdate();
      }

      // 使用同步方式重置标志位
      this.isUpdatingFromUpload = false;

      console.log('[DragAdd] Completed, final list length:', this.list.length);
    },

    handleDragRemove(evt) {
      console.log('[DragRemove] Event triggered:', {
        dragGroup: this.dragGroup,
        oldIndex: evt.oldIndex,
        listLength: this.list.length,
        nowEmpty: this.list.length === 0,
        removedItem: evt.item
      });

      this.isUpdatingFromUpload = true;

      // 立即触发状态更新，确保空容器状态切换
      this.$emit('dragChange', evt);
      this.$emit('change', this.list);

      // 如果容器变为空，强制更新视图以显示占位元素
      if (this.list.length === 0) {
        console.log('[DragRemove] Container is now empty, forcing view update');
        this.$forceUpdate();
      }

      // 使用同步方式重置标志位，避免延迟
      this.isUpdatingFromUpload = false;

      console.log('[DragRemove] Completed, final list length:', this.list.length);
    },



    // 获取文件的OCR结果
    getOcrResult(file) {
      const key = file.uid || file.mediaUrl || file.url;

      console.log('[OCR] Getting OCR result for file:', {
        fileName: file.name,
        key: key,
        hasGlobalManager: !!this.globalOcrManager,
        dragGroup: this.dragGroup
      });

      // 优先使用全局 OCR 数据管理器
      if (this.globalOcrManager && typeof this.globalOcrManager.getGlobalOcrData === 'function') {
        const globalData = this.globalOcrManager.getGlobalOcrData(file);
        if (globalData) {
          console.log('[OCR] Found global OCR data:', {
            fileName: file.name,
            extracted: globalData.extracted,
            fieldsCount: globalData.fields ? globalData.fields.length : 0,
            loading: globalData.loading
          });

          // 同步到本地数据，确保响应式更新
          if (!this.ocrResults[key] ||
            this.ocrResults[key].extracted !== globalData.extracted ||
            this.ocrResults[key].fields.length !== globalData.fields.length) {
            this.$set(this.ocrResults, key, {
              loading: globalData.loading,
              extracted: globalData.extracted,
              fields: [...(globalData.fields || [])]
            });
            console.log('[OCR] Synced global data to local for file:', file.name);
          }

          return this.ocrResults[key];
        } else {
          console.log('[OCR] No global OCR data found for file:', file.name);
        }
      }

      // 回退到本地 OCR 数据
      if (!this.ocrResults[key]) {
        this.$set(this.ocrResults, key, {
          loading: false,
          extracted: false,
          fields: []
        });
        console.log('[OCR] Created new local OCR data for file:', file.name);
      }

      return this.ocrResults[key];
    },

    // 判断图片是否准备好进行OCR提取
    isImageReady(file) {
      if (file.status === 'success') {
        return true;
      }
      if (file.url || file.mediaUrl) {
        return true;
      }
      if (file.response && file.response.data && file.response.data.mediaUrl) {
        return true;
      }
      return false;
    },

    // 清理已删除文件的OCR结果
    cleanupOcrResults(currentList) {
      const currentKeys = new Set(currentList.map(file =>
        file.uid || file.mediaUrl || file.url
      ));

      // 只清理本地 OCR 数据，不清理全局数据
      // 全局数据由全局管理器统一管理，避免在拖拽过程中误删
      Object.keys(this.ocrResults).forEach(key => {
        if (!currentKeys.has(key)) {
          // 检查是否在全局管理器中存在
          let shouldDelete = true;
          if (this.globalOcrManager && typeof this.globalOcrManager.getGlobalOcrData === 'function') {
            // 通过文件对象检查全局数据，而不是直接访问 globalOcrResults
            const fileObj = { uid: key, mediaUrl: key, url: key };
            const globalData = this.globalOcrManager.getGlobalOcrData(fileObj);
            if (globalData && globalData.extracted) {
              shouldDelete = false;
              console.log('[OCR] Preserving local data for key:', key, 'due to global data existence');
            }
          }

          if (shouldDelete) {
            this.$delete(this.ocrResults, key);
            console.log('[OCR] Cleaned up local OCR data for key:', key);
          }
        }
      });
    },

    // OCR文字提取功能
    async extractTextFromImage(file) {
      const ocrResult = this.getOcrResult(file);

      console.log('[OCR] Extract text request for file:', {
        fileName: file.name,
        extracted: ocrResult.extracted,
        fieldsCount: ocrResult.fields ? ocrResult.fields.length : 0,
        loading: ocrResult.loading
      });

      if (ocrResult.extracted && ocrResult.fields && ocrResult.fields.length > 0) {
        this.$message.warning('已成功提取图片文字，勿重复操作，提取的文字信息删除后支持再次提取');
        return;
      }

      this.$set(ocrResult, 'loading', true);

      try {
        const imgUrl = this.getImageUrl(file);
        if (!imgUrl) {
          throw new Error('图片URL无效');
        }

        const ocrPath = getOcrProxyPath();

        const response = await request({
          url: ocrPath,
          method: 'POST',
          data: {
            imgUrl: imgUrl,
            type: 'GENERAL_STRUCTURE'
          }
        });

        const responseData = response.data || response;
        console.log('OCR API 完整响应数据:', responseData);

        if (responseData.code !== 0) {
          throw new Error(responseData.msg || 'OCR识别失败');
        }

        // 处理OCR结果
        const ocrData = responseData.result || {};
        console.log('OCR 结果数据:', ocrData);
        console.log('OCR Data 字段:', ocrData.Data);
        console.log('OCR Data 类型:', typeof ocrData.Data);
        console.log('OCR Data 是否为对象:', typeof ocrData.Data === 'object' && !Array.isArray(ocrData.Data));

        const fields = this.convertOcrDataToFields(ocrData);
        console.log('转换后的字段数组:', fields);

        // 更新本地 OCR 结果
        this.$set(ocrResult, 'loading', false);
        this.$set(ocrResult, 'extracted', true);
        this.$set(ocrResult, 'fields', fields);

        // 同步到全局 OCR 数据管理器
        if (this.globalOcrManager && typeof this.globalOcrManager.setGlobalOcrData === 'function') {
          this.globalOcrManager.setGlobalOcrData(file, {
            loading: false,
            extracted: true,
            fields: fields
          });
          console.log('[OCR] Data synced to global manager for file:', file.name);
        }

        if (fields.length > 0) {
          this.$message.success(`成功提取到 ${fields.length} 个字段信息`);
          console.log('OCR 识别成功，字段详情:', fields);
        } else {
          this.$message.info('未识别到有效的文字信息');
          console.warn('OCR 识别结果为空，原始数据:', ocrData);
        }

      } catch (error) {
        console.error('OCR识别失败:', error);
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          response: error.response
        });

        this.$set(ocrResult, 'loading', false);
        this.$set(ocrResult, 'extracted', false);

        // 提供更详细的错误信息
        let errorMessage = 'OCR识别失败';
        if (error.message && error.message.includes('网络')) {
          errorMessage = 'OCR识别失败: 网络连接错误，请检查网络连接';
        } else if (error.message && error.message.includes('超时')) {
          errorMessage = 'OCR识别失败: 请求超时，请稍后重试';
        } else if (error.message && error.message.includes('图片URL无效')) {
          errorMessage = 'OCR识别失败: 图片URL无效，请重新上传图片';
        } else {
          errorMessage = `OCR识别失败: ${error.message || '未知错误'}`;
        }

        this.$message.error(errorMessage);
      }
    },

    // 获取图片URL
    getImageUrl(file) {
      console.log('getImageUrl called for file:', {
        name: file.name,
        uid: file.uid,
        status: file.status,
        hasUrl: !!file.url,
        hasMediaUrl: !!file.mediaUrl,
        hasResponse: !!file.response,
        responseData: file.response ? file.response.data : null
      });

      // 优先使用 mediaUrl（通常是上传后的正确URL）
      if (file.mediaUrl) {
        console.log('Using mediaUrl:', file.mediaUrl);
        return file.mediaUrl;
      }

      // 其次使用 url
      if (file.url) {
        console.log('Using url:', file.url);
        return file.url;
      }

      // 最后尝试从 response 中获取
      if (file.response && file.response.data) {
        if (file.response.data.mediaUrl) {
          console.log('Using response.data.mediaUrl:', file.response.data.mediaUrl);
          return file.response.data.mediaUrl;
        }
        if (file.response.data.url) {
          console.log('Using response.data.url:', file.response.data.url);
          return file.response.data.url;
        }
      }

      console.warn('No valid URL found for file:', file);
      return null;
    },

    // 转换OCR数据为字段格式
    convertOcrDataToFields(ocrData) {
      const fields = [];
      let fieldId = 1;

      console.log('convertOcrDataToFields 开始处理数据:', ocrData);

      // 检查是否有 Data 字段
      if (!ocrData.Data) {
        console.warn('OCR 数据中没有 Data 字段');
        return fields;
      }

      // 处理新格式：Data 是一个对象，包含直接的键值对
      if (typeof ocrData.Data === 'object' && !Array.isArray(ocrData.Data)) {
        console.log('处理对象格式的 OCR 数据');
        Object.entries(ocrData.Data).forEach(([key, value]) => {
          const validatedField = this.validateAndCleanOcrField(key, value);
          if (validatedField) {
            console.log(`添加字段: ${validatedField.key} = ${validatedField.value}`);
            fields.push({
              id: fieldId++,
              label: validatedField.key,
              value: validatedField.value
            });
          } else {
            console.warn('跳过无效的键值对:', { key, value });
          }
        });
      }
      // 处理旧格式：Data 是一个数组，每个元素包含 key 和 value 属性（向后兼容）
      else if (Array.isArray(ocrData.Data)) {
        console.log('处理数组格式的 OCR 数据');
        ocrData.Data.forEach((item, index) => {
          if (item && typeof item === 'object' && item.key && item.value) {
            const validatedField = this.validateAndCleanOcrField(item.key, item.value);
            if (validatedField) {
              console.log(`添加字段 [${index}]: ${validatedField.key} = ${validatedField.value}`);
              fields.push({
                id: fieldId++,
                label: validatedField.key,
                value: validatedField.value
              });
            } else {
              console.warn('跳过无效的数组项:', item);
            }
          } else {
            console.warn('跳过格式错误的数组项:', item);
          }
        });
      }
      // 未知格式
      else {
        console.error('未知的 OCR 数据格式:', typeof ocrData.Data, ocrData.Data);
      }

      console.log(`convertOcrDataToFields 完成，共转换 ${fields.length} 个字段:`, fields);
      return fields;
    },

    // 验证和清理 OCR 字段数据
    validateAndCleanOcrField(key, value) {
      // 检查基本有效性
      if (!key || !value || typeof key !== 'string' || typeof value !== 'string') {
        return null;
      }

      // 清理空白字符
      const cleanKey = key.trim();
      const cleanValue = value.trim();

      // 检查清理后是否为空
      if (!cleanKey || !cleanValue) {
        return null;
      }

      // 过滤掉一些无意义的字段
      const invalidKeys = ['', ' ', '　', '\n', '\r', '\t'];
      const invalidValues = ['', ' ', '　', '\n', '\r', '\t', '无', '无数据', 'null', 'undefined'];

      if (invalidKeys.includes(cleanKey) || invalidValues.includes(cleanValue)) {
        return null;
      }

      // 检查是否是有效的中文或英文字段
      const validKeyPattern = /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/;
      if (!validKeyPattern.test(cleanKey)) {
        console.warn('跳过无效的字段名:', cleanKey);
        return null;
      }

      return {
        key: cleanKey,
        value: cleanValue
      };
    },

    // 测试 OCR 数据解析功能
    testOcrDataParsing() {
      console.log('=== 测试 OCR 数据解析功能 ===');

      // 测试新格式数据
      const testDataNew = {
        Data: {
          "品牌": "工百年健康人 千年中医约",
          "检验": "检",
          "执行标准": "(中国药典)2027年一部",
          "公司": "本经药业",
          "品名": "旋服花",
          "药材产地": "山东滨州"
        }
      };

      console.log('测试新格式数据:', testDataNew);
      const fieldsNew = this.convertOcrDataToFields(testDataNew);
      console.log('新格式解析结果:', fieldsNew);

      // 测试旧格式数据
      const testDataOld = {
        Data: [
          { key: "品牌", value: "工百年健康人" },
          { key: "检验", value: "检" },
          { key: "执行标准", value: "(中国药典)2027年一部" }
        ]
      };

      console.log('测试旧格式数据:', testDataOld);
      const fieldsOld = this.convertOcrDataToFields(testDataOld);
      console.log('旧格式解析结果:', fieldsOld);

      // 测试无效数据
      const testDataInvalid = {
        Data: {
          "": "空键",
          "有效字段": "",
          "正常字段": "正常值",
          " ": "空格键",
          "无效值": "null"
        }
      };

      console.log('测试无效数据:', testDataInvalid);
      const fieldsInvalid = this.convertOcrDataToFields(testDataInvalid);
      console.log('无效数据解析结果:', fieldsInvalid);

      console.log('=== OCR 数据解析测试完成 ===');
    },

    // 调试图片显示状态
    debugImageDisplay() {
      console.log('=== 调试图片显示状态 ===');
      console.log('当前 list 长度:', this.list.length);
      console.log('enableDrag:', this.enableDrag);
      console.log('disabled:', this.disabled);
      console.log('limit:', this.limit);
      console.log('是否显示上传按钮:', !this.disabled && (!this.limit || this.list.length < this.limit));
      console.log('当前 list 数据:', this.list.map((file, index) => ({
        index,
        name: file.name,
        uid: file.uid,
        status: file.status,
        url: file.url,
        mediaUrl: file.mediaUrl,
        hasValidSrc: !!(file.mediaUrl || file.url || (file.response && file.response.data && file.response.data.mediaUrl))
      })));
      console.log('DOM 中的图片容器数量:', document.querySelectorAll('.image-item-container').length);
      console.log('DOM 中的上传按钮数量:', document.querySelectorAll('.upload-button-container').length);
      console.log('统一网格容器:', document.querySelector('.unified-image-grid'));
      console.log('=== 图片显示状态调试完成 ===');
    },

    // 生成唯一的文件 key
    getFileKey(file, index) {
      // 优先使用 uid，然后是 mediaUrl，最后使用 index 作为后备
      return file.uid || file.mediaUrl || file.url || `file-${index}-${file.name || 'unknown'}`;
    },

    // 调试上传数据映射
    debugUploadMapping() {
      console.log('=== 调试上传数据映射 ===');
      console.log('文件列表总数:', this.list.length);
      console.log('fileNameList:', this.fileNameList);

      this.list.forEach((file, index) => {
        console.log(`文件 ${index + 1}:`, {
          name: file.name,
          uid: file.uid,
          status: file.status,
          url: file.url,
          mediaUrl: file.mediaUrl,
          mediaName: file.mediaName,
          originalUrl: file.originalUrl,
          hasResponse: !!file.response,
          responseData: file.response ? file.response.data : null,
          isUsingBlobUrl: file.url && file.url.startsWith('blob:'),
          isUsingValidMediaUrl: file.mediaUrl && !file.mediaUrl.startsWith('blob:')
        });
      });
      console.log('=== 上传数据映射调试完成 ===');
    },

    // 调试文件列表变化
    debugFileListChanges() {
      console.log('=== 调试文件列表变化 ===');
      console.log('当前文件数量:', this.list.length);
      console.log('文件名列表:', this.fileNameList);
      console.log('文件UID列表:', this.list.map(f => f.uid));
      console.log('文件状态:', this.list.map(f => ({ name: f.name, status: f.status })));
      console.log('=== 文件列表变化调试完成 ===');
    },





    // 删除指定文件的OCR识别结果
    deleteOcrResult(file) {
      const key = file.uid || file.mediaUrl || file.url;

      // 检查本地或全局是否有 OCR 数据
      const hasLocalData = this.ocrResults[key];
      const hasGlobalData = this.globalOcrManager && this.globalOcrManager.getGlobalOcrData(file);

      if (hasLocalData || hasGlobalData) {
        this.$confirm('确定要删除这张图片的识别结果吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除本地数据
          if (hasLocalData) {
            this.$delete(this.ocrResults, key);
          }

          // 删除全局数据
          if (hasGlobalData && this.globalOcrManager && typeof this.globalOcrManager.removeGlobalOcrData === 'function') {
            this.globalOcrManager.removeGlobalOcrData(file);
            console.log('[OCR] Data removed from global manager for file:', file.name);
          }

          this.$message.success('识别结果已删除');
        }).catch(() => {
          // 用户取消删除
        });
      }
    },

    // 在预览组件中提取OCR文字的方法
    async extractTextFromImageInPreview(file) {
      const ocrResult = this.getOcrResult(file);
      if (ocrResult.extracted) {
        this.$message.info('已成功提取图片文字，勿重复操作');
        return;
      }
      await this.extractTextFromImage(file);
    },

    // 复制字段值
    copyFieldValue(value) {
      this.copyInfo(value);
    },

    // 复制信息到剪贴板
    copyInfo(info) {
      if (info) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(info).then(() => {
            this.$message.success('复制成功');
          }).catch(() => {
            this.$message.error('复制失败');
          });
        } else {
          // 降级方案
          const textArea = document.createElement('textarea');
          textArea.value = info;
          document.body.appendChild(textArea);
          textArea.select();
          try {
            document.execCommand('copy');
            this.$message.success('复制成功');
          } catch (err) {
            this.$message.error('复制失败');
          }
          document.body.removeChild(textArea);
        }
      }
    },

    // 展开/收起OCR结果
    toggleExpanded() {
      this.isExpanded = !this.isExpanded;
    },

    /**
     * 刷新指定文件的 OCR 数据
     * 用于拖拽后强制更新 UI 显示
     */
    refreshOcrDataForFile(file) {
      const key = file.uid || file.mediaUrl || file.url;

      if (this.globalOcrManager && typeof this.globalOcrManager.getGlobalOcrData === 'function') {
        const globalData = this.globalOcrManager.getGlobalOcrData(file);
        if (globalData) {
          console.log('[OCR] Refreshing OCR data for file:', file.name, globalData);

          // 强制更新本地数据
          this.$set(this.ocrResults, key, {
            loading: globalData.loading,
            extracted: globalData.extracted,
            fields: [...(globalData.fields || [])]
          });

          // 强制更新组件
          this.$forceUpdate();

          console.log('[OCR] OCR data refreshed for file:', file.name);
        }
      }
    },

    /**
     * 刷新所有文件的 OCR 数据
     * 用于容器状态变化后的批量更新
     */
    refreshAllOcrData() {
      console.log('[OCR] Refreshing all OCR data for container:', this.dragGroup);

      this.list.forEach(file => {
        this.refreshOcrDataForFile(file);
      });

      // 强制更新整个组件
      this.$forceUpdate();

      // 触发子组件更新
      this.$nextTick(() => {
        this.$children.forEach(child => {
          if (child.$forceUpdate) {
            child.$forceUpdate();
          }
        });
      });
    },

    /**
     * 触发上传 - 用于空容器占位元素的点击事件
     */
    triggerUpload() {
      // 查找上传按钮并触发点击
      this.$nextTick(() => {
        const uploadInput = this.$el.querySelector('input[type="file"]');
        if (uploadInput) {
          uploadInput.click();
        } else {
          // 如果找不到隐藏的上传按钮，创建一个临时的
          const tempInput = document.createElement('input');
          tempInput.type = 'file';
          tempInput.accept = 'image/*';
          tempInput.multiple = this.limit > 1;
          tempInput.style.display = 'none';

          tempInput.onchange = (e) => {
            const files = Array.from(e.target.files);

            // 检查文件数量限制
            if (this.limit && this.list.length + files.length > this.limit) {
              this.$message.error(`最多只能上传 ${this.limit} 个文件`);
              document.body.removeChild(tempInput);
              return;
            }

            files.forEach(file => {
              // 为文件添加必要的属性
              file.uid = Date.now() + Math.random();
              file.status = 'uploading';

              this.beforeUpload(file).then(() => {
                // 模拟上传成功
                const response = {
                  code: 200,
                  message: 'success',
                  data: {
                    mediaUrl: URL.createObjectURL(file),
                    mediaName: file.name,
                    mediaSize: file.size,
                    mediaType: file.type
                  }
                };

                // 创建模拟的 fileList，包含当前文件
                const mockFileList = [...this.list, file];

                // 更新文件状态
                file.status = 'success';

                this.handleSuccess(response, file, mockFileList);
              }).catch(error => {
                console.error('Upload validation failed:', error);
                this.$message.error('文件上传验证失败');
                file.status = 'error';
              });
            });

            document.body.removeChild(tempInput);
          };

          document.body.appendChild(tempInput);
          tempInput.click();
        }
      });
    },



    // 图片预览相关方法
    handlePreview(file) {
      const clickedIndex = this.list.findIndex(item =>
        (item.uid && item.uid === file.uid) ||
        (item.mediaUrl && item.mediaUrl === file.mediaUrl) ||
        (item.url && item.url === file.url)
      );

      this.initialIndex = clickedIndex >= 0 ? clickedIndex : 0;

      this.imgPreviewList = this.list.map(item => {
        const responseMediaUrl = item.response && item.response.data && item.response.data.mediaUrl;
        return {
          uid: item.uid,
          name: item.name || item.mediaName || `image-${item.uid}`,
          mediaName: item.name || item.mediaName || `image-${item.uid}`,
          mediaUrl: item.url || item.mediaUrl || responseMediaUrl,
          url: item.url || item.mediaUrl || responseMediaUrl,
          status: item.status
        };
      });

      this.imgPreview = true;
    },

    closeImageViewer() {
      this.imgPreview = false;
    },

    // 图片操作方法
    handleRemove(file) {
      console.log('DraggableImageUpload: handleRemove called for file:', file.name || file.mediaName);
      const index = this.list.findIndex(item =>
        (item.uid && item.uid === file.uid) ||
        (item.mediaUrl && item.mediaUrl === file.mediaUrl) ||
        (item.url && item.url === file.url)
      );
      if (index > -1) {
        this.isUpdatingFromUpload = true;
        this.list.splice(index, 1);
        console.log('DraggableImageUpload: After remove, list length:', this.list.length);
        this.$emit('change', this.list);
        this.$nextTick(() => {
          this.isUpdatingFromUpload = false;
        });
      }
    },

    handleLeft(file) {
      console.log('DraggableImageUpload: handleLeft called for file:', file.name || file.mediaName);
      const index = this.list.findIndex(item =>
        (item.uid && item.uid === file.uid) ||
        (item.mediaUrl && item.mediaUrl === file.mediaUrl) ||
        (item.url && item.url === file.url)
      );
      if (index > 0) {
        this.isUpdatingFromUpload = true;
        const temp = this.list[index];
        this.$set(this.list, index, this.list[index - 1]);
        this.$set(this.list, index - 1, temp);
        this.$emit('change', this.list);
        this.$nextTick(() => {
          this.isUpdatingFromUpload = false;
        });
      }
    },

    handleRight(file) {
      console.log('DraggableImageUpload: handleRight called for file:', file.name || file.mediaName);
      const index = this.list.findIndex(item =>
        (item.uid && item.uid === file.uid) ||
        (item.mediaUrl && item.mediaUrl === file.mediaUrl) ||
        (item.url && item.url === file.url)
      );
      if (index < this.list.length - 1) {
        this.isUpdatingFromUpload = true;
        const temp = this.list[index];
        this.$set(this.list, index, this.list[index + 1]);
        this.$set(this.list, index + 1, temp);
        this.$emit('change', this.list);
        this.$nextTick(() => {
          this.isUpdatingFromUpload = false;
        });
      }
    },

    // 上传相关方法
    handleExceed() {
      this.$message.warning(`最多只能上传 ${this.limit} 张图片`);
    },

    handleError(err) {
      this.$message.error('图片上传失败');
      console.error('Upload error:', err);
    },

    async beforeUpload(file) {
      console.log('DraggableImageUpload: beforeUpload called', {
        fileName: file.name,
        fileSize: file.size,
        currentListLength: this.list.length,
        fileUID: file.uid
      });

      const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name);
      const isMaxSize = file.size / 1024 / 1024 < this.maxSize;

      if (!isImage) {
        this.$message.error('只能上传图片格式的文件');
        return false;
      }

      if (!isMaxSize) {
        this.$message.error(`图片大小不能超过 ${this.maxSize}MB`);
        return false;
      }

      // 检查是否已经存在相同的文件（基于文件名和大小）
      const duplicateFile = this.list.find(f =>
        f.name === file.name &&
        f.size === file.size &&
        f.status === 'success'
      );

      if (duplicateFile) {
        console.warn('DraggableImageUpload: Duplicate file detected:', file.name);
        this.$message.warning(`文件 "${file.name}" 已存在，请选择其他文件`);
        return false;
      }

      if (this.minWidth > 0) {
        const isValidWidth = await this.checkWidth(file);
        if (!isValidWidth) {
          this.$message.error(`图片宽度不能小于 ${this.minWidth}px`);
          return false;
        }
      }

      console.log('DraggableImageUpload: File validation passed:', file.name);

      if (file.size / 1024 / 1024 > 1 && isImage && isMaxSize) {
        return imageConversion.compress(file, 0.6);
      } else {
        return isImage && isMaxSize;
      }
    },

    checkWidth(file) {
      const isSize = new Promise((resolve, reject) => {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = () => {
          let valid = true;
          if (img.width < this.minWidth) valid = false;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return true;
        },
        () => {
          return false;
        }
      );
      return isSize;
    },

    handleSuccess(response, file, fileList) {
      try {
        console.log('DraggableImageUpload: handleSuccess called', {
          fileName: file ? file.name : 'unknown',
          fileUID: file ? file.uid : 'unknown',
          fileListLength: fileList ? fileList.length : 0,
          currentListLength: this.list.length,
          response: response,
          responseData: response ? response.data : null,
          currentListUIDs: this.list.map(f => f.uid),
          fileListUIDs: fileList ? fileList.map(f => f.uid) : []
        });

        // 参数验证
        if (!file) {
          console.error('DraggableImageUpload: handleSuccess called with invalid file');
          return;
        }

        if (!response || !response.data) {
          console.error('DraggableImageUpload: handleSuccess called with invalid response');
          return;
        }

        // 设置标志位，防止 watch.fileList 干扰
        this.isUpdatingFromUpload = true;

        this.fileNameList.push(file.name);

        // 检查当前上传的文件是否已经在列表中
        const existingFileIndex = this.list.findIndex(f => f.uid === file.uid);

        if (existingFileIndex !== -1) {
          // 如果文件已存在，更新该文件的数据
          console.log('DraggableImageUpload: Updating existing file in list');

          const updatedFile = this.processUploadedFile(file, response);

          // 创建新的列表，更新指定位置的文件
          const newList = [...this.list];
          newList[existingFileIndex] = updatedFile;
          this.list = newList;

        } else {
          // 如果文件不存在，添加到列表末尾
          console.log('DraggableImageUpload: Adding new file to list');

          const newFile = this.processUploadedFile(file, response);

          // 将新文件添加到现有列表的末尾
          this.list = [...this.list, newFile];
        }

        console.log('DraggableImageUpload: Final list after upload:', {
          totalFiles: this.list.length,
          files: this.list.map(f => ({
            name: f.name,
            uid: f.uid,
            status: f.status,
            mediaUrl: f.mediaUrl,
            hasValidMediaUrl: !!f.mediaUrl && !f.mediaUrl.startsWith('blob:')
          }))
        });

        this.$emit("change", this.list);

        // 重置标志位
        this.$nextTick(() => {
          this.isUpdatingFromUpload = false;
          console.log('DraggableImageUpload: Upload operation completed');
        });

      } catch (error) {
        console.error('DraggableImageUpload: Error in handleSuccess:', error);
        this.$message.error('文件处理失败');

        // 确保重置标志位
        this.isUpdatingFromUpload = false;
      }
    },

    parsePercentage(val) {
      return parseInt(val, 10);
    },

    // 处理上传成功的文件数据
    processUploadedFile(file, response) {
      console.log('DraggableImageUpload: Processing uploaded file:', file.name, {
        originalUrl: file.url,
        hasResponse: !!response,
        responseData: response ? response.data : null
      });

      // 检查响应数据格式
      let mediaUrl = null;
      let mediaName = null;

      if (response) {
        // 处理不同的响应格式
        if (response.data && response.data.mediaUrl) {
          // 标准格式：{ data: { mediaUrl: "...", mediaName: "..." } }
          mediaUrl = response.data.mediaUrl;
          mediaName = response.data.mediaName;
        } else if (response.mediaUrl) {
          // 直接格式：{ mediaUrl: "...", mediaName: "..." }
          mediaUrl = response.mediaUrl;
          mediaName = response.mediaName;
        } else if (typeof response === 'string') {
          // 字符串格式：直接是 URL
          mediaUrl = response;
        }
      }

      console.log('DraggableImageUpload: Extracted media data:', {
        mediaUrl,
        mediaName,
        isValidUrl: mediaUrl && !mediaUrl.startsWith('blob:')
      });

      return {
        ...file,
        // 使用接口返回的正确 URL
        mediaUrl: mediaUrl || file.url,
        mediaName: mediaName || file.name,
        // 保留原始的 blob URL 作为备用
        originalUrl: file.url,
        // 确保状态为成功
        status: 'success',
        // 保存完整的响应数据
        response: response
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.draggable-image-upload-container {
  .upload-ocr-layout {
    display: flex;
    gap: 20px;
  }

  .left-section {
    flex: 1;
  }

  .upload-section {
    position: relative;
  }

  // 统一图片网格样式
  .unified-image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: flex-start;
    position: relative; // 为空容器的特殊布局提供定位上下文

    // 调试样式 - 可以临时启用来检查布局
    // border: 1px solid red;
    // background-color: rgba(255, 0, 0, 0.1);
  }

  .draggable-image-list,
  .static-image-list {
    display: contents; // 让子元素直接参与父级的 flex 布局
  }

  // 空容器样式 - 使用最简单的布局
  .draggable-image-list.empty-container {
    display: flex; // 使用 flex 布局
    width: 148px;
    height: 148px;
    position: relative;
    align-items: center;
    justify-content: center;
  }

  // 拖拽占位元素样式 - 作为真正的内容元素
  .drag-placeholder {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 1; // 确保始终可见

    &:hover {
      border-color: #c0c4cc;
      background: #f5f7fa;
    }

    // 拖拽进行中时的增强样式
    .draggable-image-list.sortable-drag-active & {
      border-color: #409eff;
      background: rgba(64, 158, 255, 0.1);
      animation: pulse 1.5s infinite;
    }

    .placeholder-content {
      font-size: 12px;
      color: #909399;
      text-align: center;
      pointer-events: none;

      &::before {
        content: "📁";
        display: block;
        font-size: 24px;
        margin-bottom: 8px;
      }
    }

    // 拖拽悬停时显示完整的接收区域
    &.sortable-ghost {
      border-color: #67c23a !important;
      background-color: rgba(103, 194, 58, 0.1) !important;

      .placeholder-content {
        color: #67c23a;
        font-weight: 500;

        &::before {
          content: "📥";
        }

        &::after {
          content: "\A放置图片到此处";
          white-space: pre;
          font-size: 14px;
          margin-top: 8px;
          display: block;
        }
      }
    }
  }

  .image-item-container {
    width: 148px;
    height: 148px;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    background-color: #fbfdff;

    &:hover {
      border-color: #409eff;
    }
  }

  // 上传按钮容器样式
  .upload-button-container {
    width: 148px;
    height: 148px;
    max-width: 148px; // 强制限制最大宽度
    max-height: 148px; // 强制限制最大高度
    min-width: 148px; // 强制限制最小宽度
    min-height: 148px; // 强制限制最小高度
    flex-shrink: 0; // 防止在 flex 布局中被压缩
    flex-grow: 0; // 防止在 flex 布局中被拉伸
    display: flex; // 确保内部元素正确对齐
    box-sizing: border-box; // 确保边框包含在尺寸内
    overflow: hidden; // 防止内容溢出
    position: relative; // 为绝对定位的拖拽区域提供定位上下文

    &.drag-enabled {

      // 拖拽启用时的样式
      .inline-upload ::v-deep .el-upload {
        transition: all 0.3s ease;

        &.is-dragover {
          border-color: #67c23a;
          background-color: rgba(103, 194, 58, 0.1);

          .upload-content {
            color: #67c23a;

            .upload-icon {
              color: #67c23a;
            }
          }
        }
      }
    }



    .inline-upload {
      width: 100% !important;
      height: 100% !important;
      max-width: 100% !important;
      max-height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      box-sizing: border-box !important;
      overflow: hidden !important;

      // 确保 inline-upload 也参与居中布局
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      // 重置 el-upload 的默认样式
      ::v-deep .el-upload {
        // 强制重置所有尺寸属性
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
        min-width: 100% !important;
        min-height: 100% !important;

        // 强制重置所有间距属性
        margin: 0 !important;
        padding: 0 !important;
        border: 1px dashed #d9d9d9 !important;
        border-radius: 6px !important;

        // 强制重置定位和显示属性
        position: relative !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        background-color: #fbfdff !important;
        cursor: pointer !important;

        // 强制重置字体和行高属性
        font-size: 0 !important; // 重置字体大小，避免影响布局
        line-height: 0 !important; // 重置行高，避免影响布局

        // 强制使用 flex 布局实现完美居中
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-direction: column !important;

        // 确保内容在容器中完全居中
        text-align: center !important;

        &:hover {
          border-color: #409eff;
        }

        .upload-content {
          // 使用绝对定位实现精确居中
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;

          // 强制重置所有尺寸和间距
          width: auto !important;
          height: auto !important;
          margin: 0 !important;
          padding: 0 !important;
          border: none !important;

          // 强制重置字体属性
          font-size: 12px !important; // 重置为明确的字体大小
          line-height: 1 !important; // 重置行高
          color: #8c939d !important;
          text-align: center !important;

          // 使用 flex 布局排列内部元素
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;

          .upload-icon {
            // 强制重置图标样式
            font-size: 28px !important;
            line-height: 1 !important;
            display: block !important;
            width: 100% !important;
            height: auto !important;
            margin: 0 0 4px 0 !important; // 进一步减少底部间距
            padding: 0 !important;
            border: none !important;
            text-align: center !important;
            color: #8c939d !important;

            // vertical-align 对 block 元素无效，已移除
          }

          .upload-hint {
            // 强制重置提示文本容器
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;

            .click-hint {
              // 强制重置第一行文本
              margin: 0 !important; // 完全移除间距
              padding: 0 !important;
              font-size: 12px !important;
              line-height: 1 !important; // 进一步减少行高
              text-align: center !important;
              width: 100% !important;
              height: auto !important;
              color: #8c939d !important;
              border: none !important;
              display: block !important;
            }

            .drag-hint {
              // 强制重置第二行文本
              margin: 0 !important;
              padding: 0 !important;
              font-size: 11px !important;
              line-height: 1 !important; // 进一步减少行高
              color: #c0c4cc !important;
              text-align: center !important;
              width: 100% !important;
              height: auto !important;
              border: none !important;
              display: block !important;
            }
          }
        }
      }

      // 隐藏 el-upload 的文件列表
      ::v-deep .el-upload-list {
        display: none;
      }
    }
  }

  .draggable-item {
    cursor: move;
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.02);
      z-index: 10;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 2px dashed transparent;
      border-radius: 6px;
      pointer-events: none;
      transition: border-color 0.2s ease;
    }

    &:hover::before {
      border-color: #409eff;
    }
  }

  .sortable-ghost {
    opacity: 0.3;
    transform: scale(0.9);

    &::before {
      border-color: #67c23a !important;
      background-color: rgba(103, 194, 58, 0.1);
    }
  }

  .sortable-chosen {
    transform: scale(1.05);
    z-index: 20;

    &::before {
      border-color: #409eff !important;
      background-color: rgba(64, 158, 255, 0.1);
    }
  }

  .sortable-drag {
    opacity: 0.8;
    transform: rotate(5deg);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  // OCR结果展示区域样式
  .right-section {
    margin-left: 20px;
    width: 300px;
    flex-shrink: 0;

    .ocr-results-section {
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #f8f9fa;

      .ocr-results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        border-bottom: 1px solid #e4e7ed;
        background-color: #fff;
        border-radius: 6px 6px 0 0;

        .ocr-results-title {
          font-weight: 600;
          color: #303133;
        }

        .expand-toggle-btn {
          color: #409eff;
          cursor: pointer;
          font-size: 12px;

          &:hover {
            color: #66b1ff;
          }
        }
      }

      .ocr-results-content {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;

        &.expanded {
          max-height: none;
        }

        .ocr-result-group {
          margin-bottom: 15px;
          padding: 10px;
          background-color: #fff;
          border-radius: 4px;
          border: 1px solid #e4e7ed;

          &:last-child {
            margin-bottom: 0;
          }

          .ocr-result-image-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;

            .image-name {
              font-size: 12px;
              color: #606266;
              font-weight: 500;
            }

            .delete-result-btn {
              color: #f56c6c;
              cursor: pointer;

              &:hover {
                color: #f78989;
              }
            }
          }

          .ocr-result-fields {
            .ocr-result-field {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .field-content {
                flex: 1;
                margin-right: 8px;

                .field-label {
                  font-size: 12px;
                  color: #909399;
                  font-weight: 500;
                }

                .field-value {
                  font-size: 12px;
                  color: #303133;
                  word-break: break-all;
                }
              }

              .field-copy {
                .copy-btn {
                  color: #409eff;
                  cursor: pointer;
                  font-size: 12px;

                  &:hover {
                    color: #66b1ff;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 当拖拽功能禁用时的样式
.draggable-image-upload-container.drag-disabled {
  .draggable-item {
    cursor: default;

    &:hover {
      transform: none;
    }

    &::before {
      display: none;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .draggable-image-upload-container {
    .unified-image-grid {
      gap: 6px;
    }

    .image-item-container,
    .upload-button-container,
    .drag-placeholder {
      width: 120px;
      height: 120px;
    }

    .drag-placeholder {
      .placeholder-content {
        font-size: 10px;

        &::before {
          font-size: 20px;
          margin-bottom: 4px;
        }
      }

      &.sortable-ghost {
        .placeholder-content {
          &::after {
            font-size: 12px;
            margin-top: 4px;
          }
        }
      }
    }

    .upload-button-container {
      // 确保移动端也保持正确的 flex 布局
      display: flex;

      .inline-upload {
        // 移动端也确保 inline-upload 参与居中
        display: flex;
        align-items: center;
        justify-content: center;

        ::v-deep .el-upload {
          // 移动端也强制使用 flex 居中
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          margin: 0 !important;
          padding: 0 !important;
          min-height: 100%;
          min-width: 100%;

          .upload-content {
            // 移动端也使用绝对定位实现精确居中
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;

            // 移动端也确保内容完全居中
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            width: auto !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;

            .upload-icon {
              font-size: 24px !important;
              margin: 0 0 3px 0 !important; // 移动端进一步减少间距
              padding: 0 !important;
              line-height: 1 !important;
              text-align: center !important;
              width: auto !important;
              color: #8c939d !important;
            }

            .upload-hint {
              display: flex !important;
              flex-direction: column !important;
              align-items: center !important;
              width: 100% !important;
              margin: 0 !important;
              padding: 0 !important;

              .click-hint {
                font-size: 11px !important;
                margin: 0 !important; // 移动端完全移除间距
                padding: 0 !important;
                text-align: center !important;
                width: auto !important;
                line-height: 1 !important;
                color: #8c939d !important;
              }

              .drag-hint {
                font-size: 10px !important;
                margin: 0 !important;
                padding: 0 !important;
                text-align: center !important;
                width: auto !important;
                line-height: 1 !important;
                color: #c0c4cc !important;
              }
            }
          }
        }
      }
    }
  }
}

// 禁用状态样式
.draggable-image-upload-container {
  &.disabled {
    .upload-button-container {
      display: none;
    }
  }
}

// 脉冲动画 - 用于拖拽过程中的视觉反馈
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.02);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
