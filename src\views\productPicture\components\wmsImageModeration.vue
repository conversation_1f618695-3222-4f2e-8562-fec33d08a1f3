<template>
    <div class="wms-image-moderation-page">
        <!-- 页面主体内容区域 - 可滚动 -->
        <div class="page-main-content">
            <!-- 商品信息区域 -->
            <div class="content-section">
                <div class="section-header">
                    <span class="title">商品信息</span>
                </div>
                <div class="section-content">
                    <div class="product-info">
                        <vxe-table border highlight-hover-row auto-resize resizable align="center" :data="rowData"
                            ref="table">
                            <vxe-table-column field="productId" title="标准库id" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="productCode" title="商品编码" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="originalProductCode" title="原商品编码" min-width="120"
                                show-header-overflow show-overflow></vxe-table-column>
                            <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="spuCategoryName" title="商品大类" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow
                                show-overflow></vxe-table-column>
                            <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120"
                                show-header-overflow show-overflow></vxe-table-column>
                        </vxe-table>
                    </div>
                </div>
            </div>

            <!-- 图片处理信息区域 -->
            <div class="content-section">
                <div class="section-header">
                    <span class="title">处理信息</span>
                    <span class="sub-title">{{ title }}</span>
                </div>
                <div class="section-content">
                    <div class="preview-box">
                        <draggable class="drag-wrap" v-model="draggableData">
                            <el-row :gutter="16">
                                <el-col :span="row.title == '器械许可证图片列表' ? 24 : 8"
                                    v-for="(row, parIndex) in draggableData" :key="parIndex">
                                    <el-card class="image-category-card">
                                        <div class="category-title">{{ row.title }}</div>
                                        <draggable class="img-wrap" :list="row.imgList" :group="{ name: 'row' }">
                                            <el-col :span="row.title == '器械许可证图片列表' ? 8 : 24"
                                                v-for="(item, index) in row.imgList" :key="index">
                                                <el-card class="image-item-card">
                                                    <span
                                                        v-if="item.pictureStatus !== 1 && item.deleteStatus === 0 && showBtn"
                                                        @click="deleteImg(item, 'delete')" class="delete-btn">删除</span>
                                                    <span
                                                        v-if="item.pictureStatus !== 1 && item.deleteStatus === 1 && showBtn"
                                                        class="delete-btn cancel"
                                                        @click="deleteImg(item, 'cancel')">取消删除</span>
                                                    <img @click="previewImg(index, parIndex)" :class="{
                                                        abnormal: item.styleState == 1,
                                                        normal: item.styleState == 2,
                                                    }" :src="item.pictureUrl" alt="" />
                                                    <span class="image-name"
                                                        v-if="rowData[skuIndex] && rowData[skuIndex].applyCode.indexOf('STP') == -1">{{
                                                            item.pictureName }}</span>
                                                </el-card>
                                            </el-col>
                                        </draggable>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </draggable>

                        <!-- 换绑信息 -->
                        <div class="update-bind-box"
                            v-if="rowData[skuIndex] && rowData[skuIndex].pictureVersionType === 5">
                            <p>换绑机构：</p>
                            <p v-for="(item, index) in allData.bindMechanismList" :key="index">{{ item }}</p>
                            <br>
                            <p>原绑定版本号：</p>
                            <img :src="allData.sourcePictureVersionMainPictureUrl" alt="">
                            <p>{{ allData.sourcePictureVersion }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 固定底部操作按钮区域 -->
        <div class="fixed-bottom-actions" v-if="showBtn">
            <div class="actions-container">
                <el-button type="primary" size="large" @click="pass" class="action-btn" :loading="passLoading">
                    <i class="el-icon-check"></i>
                    审核通过
                </el-button>
                <el-button type="danger" size="large" @click="refuse" class="action-btn">
                    <i class="el-icon-close"></i>
                    运营驳回
                </el-button>
            </div>
        </div>

        <preview-img ref="previewImg" :productInfo="rowData[skuIndex]" :webList="allData"></preview-img>
    </div>
</template>

<script>
import {
    getOriginalTaskReceivedDetail,
    auditTaskReceived,
    getBaoCaoPictureOne
} from "@/api/productPicture.js";
import draggable from "vuedraggable";
import previewImg from "@/components/uploadImg/previewImg";
export default {
    name: "",
    components: {
        draggable,
        previewImg
    },
    filters: {},
    props: {},
    watch: {},
    data() {
        return {
            showBtn: true,
            rowData: {},
            draggableData: [],
            preImgList: [],
            preIndex: 0,
            title: "",
            allData: [],
            skuIndex: 0,
            auditOption: "",
            loading: false,
            productInfo: [],
            passLoading: false,
        };
    },
    computed: {},
    watch: {},
    created() {
        this.initPageData();
    },
    mounted() { },
    methods: {
        getPictureOneData(id) {
            getBaoCaoPictureOne(id).then(res => {
                if (res.retCode === 0) {

                }
            })
        },
        // 预览
        previewImg(index, parIndex) {
            let flag = true;
            this.preIndex = 0;
            this.preImgList = [];
            this.draggableData.forEach((list, dParIndex) => {
                list.imgList.forEach((item, dIndex) => {
                    if (dParIndex == parIndex && dIndex == index) {
                        flag = false;
                    }
                    if (flag) {
                        this.preIndex++;
                    }
                    this.preImgList.push(item);
                });
            });
            this.$refs.previewImg.openDlg(
                this.preImgList,
                this.preIndex,
                this.rowData[this.skuIndex].smallPackageCode
            );
        },
        // 初始化页面数据
        initPageData() {
            // 从路由参数获取数据
            const { id, skuIndex = 0 } = this.$route.query;
            if (id) {
                this.skuIndex = parseInt(skuIndex);
                this.loadTaskDetail(id);
            } else {
                this.$message.error('缺少必要参数');
                this.goBack();
            }
        },
        // 加载任务详情
        async loadTaskDetail(id) {
            this.loading = true;
            try {
                const res = await getBaoCaoPictureOne(id);
                // console.log(res);
                if (res.retCode === 0) {
                    this.allData = {
                        createUser: res.data.pictureTaskDetailVo.createUser,
                        createUserMechanismName: res.data.pictureTaskDetailVo.createUserMechanismName,
                        pictureVersion: res.data.pictureTaskDetailVo.pictureVersion,
                        sourcePictureVersionMainPictureUrl: res.data.pictureTaskDetailVo.sourcePictureVersionMainPictureUrl,
                        sourcepictureVersion: res.data.pictureTaskDetailVo.sourcepictureVersion,
                        productPictureList: res.data.productPictureList,
                    };
                    this.rowData = [res.data.pictureTaskDetailVo];
                    this.rowData[this.skuIndex].bindMechanismList = res.data.pictureTaskDetailVo.bindMechanismList || [];
                    this.rowData[this.skuIndex].applyCode = String(this.$route.query.applyCode);
                    this.showBtn = this.rowData[this.skuIndex].auditStatus ? false : true;
                    this.getDragDate();
                    this.getDiaTitle();
                } else {
                    this.$message.error(res.retMsg);
                    this.goBack();
                }
            } finally {
                this.loading = false;
            }
        },
        // 返回上一页
        goBack() {
            try {
                this.$router.go(-1);
                parent.CreateTab(
                    `../static/dist/index.html#/productPicture/taskToBeAudit?businessType=${this.$route.query.businessType}`,
                    "待审核任务列表"
                );
                parent.CloseTab("../static/dist/index.html#/productPicture/wmsImageModeration");
            } catch (e) {
                console.error(e);
            }
        },
        // 页面标题
        getDiaTitle() {
            const data = this.allData;
            if (data.createUserMechanismName) {
                this.title = `${data.pictureVersion}_${data.createUser}_${data.createUserMechanismName}`;
            } else {
                this.title = `${data.pictureVersion}_${data.createUser}`;
            }
        },
        // 图片列表数据格式处理
        getDragDate() {
            if (this.rowData[this.skuIndex].applyCode.indexOf("STP") != -1) {
                this.draggableData = [{ imgList: [], title: "器械许可证图片列表" }];
                this.allData.productPictureList.forEach(
                    (item) => {
                        this.draggableData[0].imgList.push(item);
                    }
                );
            } else {
                this.draggableData = [
                    { imgList: [], title: "主图" },
                    { imgList: [], title: "外包装" },
                    { imgList: [], title: "说明书" },
                ];
                this.allData.productPictureList.forEach(
                    (item) => {
                        let img = new Image();
                        img.src = item.pictureUrl;
                        // 别人写的看不懂的样式代码
                        img.onload = () => {
                            if (item.readonly || item.auditStatus == 1) {
                                if (item.pictureOrdinal == 1) {
                                    if (img.height < 800 || img.height < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 0, this.draggableData[0]);
                                } else if (
                                    item.pictureOrdinal >= 2 &&
                                    item.pictureOrdinal <= 5
                                ) {
                                    if (img.width < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 1, this.draggableData[1]);
                                } else {
                                    if (img.width < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 2, this.draggableData[2]);
                                }
                            }
                        };
                        if (item.pictureOrdinal == 1) {
                            this.draggableData[0].imgList.push(item);
                        } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
                            this.draggableData[1].imgList.push(item);
                        } else {
                            this.draggableData[2].imgList.push(item);
                        }
                    }
                );
            }
        },
        // 删除原图
        deleteImg(item, type) {
            if (type == "delete") {
                item.deleteStatus = 1;
            } else {
                item.deleteStatus = 0;
            }
        },
        // 运营驳回
        refuse() {
            this.$prompt("审核不通过原因", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /\S/,
                inputErrorMessage: "审核不通过原因必填",
            })
                .then(({ value }) => {
                    this.auditOption = value;
                    this.submitDealData(2);
                })
                .catch(() => {
                    this.auditOption = ''
                    this.$message({
                        type: "info",
                        message: "取消输入",
                    });
                });
        },
        // 审核通过
        pass() {
            this.submitDealData(1);
        },
        submitDealData(auditStatus) {
            let arr = [];
            let flag = true;
            this.draggableData.forEach((list, parentIndex) => {
                let normalArr = list.imgList.filter((item) => {
                    return item.deleteStatus == 0;
                });
                if (this.rowData[this.skuIndex].applyCode.indexOf("STP") == -1) {
                    if (parentIndex == 0 && normalArr.length != 1) {
                        this.$message.error("主图数量必须为1张");
                        flag = false;
                        return;
                    }
                    if (parentIndex == 1 && normalArr.length > 4) {
                        this.$message.error("外包装最多只能4张");
                        flag = false;
                        return;
                    }
                    if (parentIndex == 2 && normalArr.length > 5) {
                        this.$message.error("说明书最多只能5张");
                        flag = false;
                        return;
                    }
                } else {
                    if (parentIndex == 0 && normalArr.length > 10) {
                        this.$message.error("器械许可证图片数量最多只能10张");
                        flag = false;
                        return;
                    }
                }
                if (flag) {
                    let index = 0;
                    list.imgList.forEach((item) => {
                        if (this.rowData[this.skuIndex].applyCode.indexOf("STP") == -1) {
                            if (parentIndex == 0) {
                                item.pictureOrdinal = 1;
                            } else if (parentIndex == 1) {
                                if (item.deleteStatus == 1) {
                                    item.pictureOrdinal = 2;
                                } else {
                                    item.pictureOrdinal = index + 2;
                                    if (index != 3) {
                                        index++;
                                    }
                                }
                            } else if (parentIndex == 2) {
                                if (item.deleteStatus == 1) {
                                    item.pictureOrdinal = 6;
                                } else {
                                    item.pictureOrdinal = index + 6;
                                    if (index != 4) {
                                        index++;
                                    }
                                }
                            }
                        } else {
                            if (item.deleteStatus == 1) {
                                item.pictureOrdinal = 11;
                            } else {
                                item.pictureOrdinal = index + 11;
                                index++;
                            }
                        }

                        let obj = {
                            pictureId: item.pictureId,
                            pictureOrdinal: item.pictureOrdinal,
                            deleteStatus: item.deleteStatus
                        };
                        arr.push(obj);
                    });
                }
            });
            if (flag) {
                let state = auditStatus
                this.postReviewPictures(arr, state);
            }
        },
        postReviewPictures(productPictureList, auditStatus) {
            this.passLoading = true
            auditTaskReceived({
                id: this.$route.query.id,
                auditStatus,
                auditOption: this.auditOption,
                productPictureList,
            }).then((res) => {
                if (res.retCode == 0) {
                    this.$message.success("操作成功！");
                    // 操作成功后返回上一页
                    // this.goBack();
                    if (res.data) {
                        this.$router.push({
                            path: "/productPicture/dealphoto",
                            query: {
                                page: '1',
                                id: res.data,
                                from: 'selfSupport'
                            },
                        });
                    } else {
                        this.goBack();
                    }
                } else {
                    this.$message.error(res.retMsg);
                }
            }).finally(() => {
                this.passLoading = false
            });
        }
    },
};
</script>

<style lang="scss" scoped>
// 页面整体布局
.wms-image-moderation-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f7fa;
    overflow: hidden;
}

// 主体内容区域 - 可滚动
.page-main-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 100px; // 为底部按钮留出空间

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

// 内容区块
.content-section {
    margin-bottom: 24px;

    &:last-child {
        margin-bottom: 0;
    }
}

// 区块头部
.section-header {
    background: #fff;
    padding: 20px 32px;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 0;
    z-index: 10;

    .title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin-right: 24px;
        display: inline-block;
    }

    .sub-title {
        font-size: 14px;
        color: #606266;
        font-weight: normal;
    }
}

// 区块内容
.section-content {
    padding: 24px 32px;
    background: #fff;
    margin-bottom: 1px; // 创建视觉分隔
}

// 商品信息表格区域
.product-info {
    /deep/ .vxe-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        width: 100% !important;
    }
}

// 图片预览区域
.preview-box {
    width: 100%;
}

// 拖拽容器
.drag-wrap {
    width: 100%;

    /deep/ .el-row {
        margin: 0 -8px;
    }

    /deep/ .el-col {
        padding: 0 8px;
        margin-bottom: 16px;
    }
}

// 图片分类卡片
.image-category-card {
    border-radius: 12px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    /deep/ .el-card__body {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
}

// 分类标题
.category-title {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 16px;
    border-bottom: 2px solid #409eff;
    margin-bottom: 16px;
    position: relative;

    &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, #409eff, #67c23a);
        border-radius: 1px;
    }
}

// 图片项容器
.img-wrap {
    flex: 1;

    /deep/ .el-col {
        margin-bottom: 12px;
    }
}

// 图片项卡片
.image-item-card {
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    background: #fafbfc;

    &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    /deep/ .el-card__body {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: start;
        position: relative;
        padding: 16px;
        min-height: 140px;
    }
}

// 删除按钮
.delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 11px;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    background: #f56c6c;
    transition: all 0.3s ease;
    font-weight: 500;
    z-index: 10;

    &:hover {
        background: #f78989;
        transform: scale(1.05);
    }

    &.cancel {
        background: #909399;

        &:hover {
            background: #a6a9ad;
        }
    }
}

// 图片样式
.image-item-card img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 6px;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &.abnormal {
        border-color: #f56c6c;
        box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
    }

    &.normal {
        border-color: #67c23a;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
    }
}

// 图片名称
.image-name {
    text-align: center;
    margin-top: 8px;
    margin-left: 23px;
    font-size: 16px;
    color: #606266;
    line-height: 1.4;
    word-break: break-all;
    max-width: 100%;
}

// 换绑信息区域
.update-bind-box {
    margin-top: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;

    p {
        margin: 8px 0;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;

        &:first-child {
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
        }
    }

    img {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #e4e7ed;
        margin: 12px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

// 固定底部操作按钮区域
.fixed-bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #e4e7ed;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
    z-index: 1000;
    padding: 16px 0;
}

.actions-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 32px;
}

.action-btn {
    min-width: 140px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;

    i {
        margin-right: 8px;
        font-size: 18px;
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &.el-button--primary {
        border: none;
    }

    &.el-button--danger {
        border: none;
    }
}

// 响应式设计
@media screen and (max-width: 1366px) {
    .section-content {
        padding: 20px 24px;
    }

    .section-header {
        padding: 16px 24px;

        .title {
            font-size: 16px;
        }
    }

    .category-title {
        font-size: 16px;
    }

    .image-item-card img {
        width: 80px;
        height: 80px;
    }

    .image-item-card /deep/ .el-card__body {
        min-height: 120px;
        padding: 12px;
    }

    .actions-container {
        padding: 0 24px;
        gap: 16px;
    }

    .action-btn {
        min-width: 120px;
        height: 44px;
        font-size: 14px;
    }
}

@media screen and (max-width: 1200px) {
    .section-content {
        padding: 16px 20px;
    }

    .section-header {
        padding: 14px 20px;
    }

    .page-main-content {
        padding-bottom: 90px;
    }

    .actions-container {
        padding: 0 20px;
        gap: 12px;
    }

    .action-btn {
        min-width: 100px;
        height: 40px;
        font-size: 14px;

        i {
            margin-right: 6px;
            font-size: 16px;
        }
    }

    .fixed-bottom-actions {
        padding: 12px 0;
    }
}

@media screen and (max-width: 768px) {
    .section-content {
        padding: 12px 16px;
    }

    .section-header {
        padding: 12px 16px;

        .title {
            font-size: 14px;
        }

        .sub-title {
            font-size: 12px;
            display: block;
            margin-top: 4px;
        }
    }

    .page-main-content {
        padding-bottom: 80px;
    }

    .actions-container {
        flex-direction: column;
        padding: 0 16px;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
        min-width: auto;
        height: 36px;
        font-size: 13px;
    }

    .fixed-bottom-actions {
        padding: 8px 0;
    }

    .image-item-card img {
        width: 60px;
        height: 60px;
    }

    .image-item-card /deep/ .el-card__body {
        min-height: 100px;
        padding: 8px;
    }
}

@media screen and (min-width: 1920px) {
    .section-content {
        max-width: 1600px;
        margin: 0 auto;
        padding: 32px 40px;
    }

    .section-header {
        max-width: 1600px;
        margin: 0 auto;
        padding: 24px 40px;
    }

    .image-item-card img {
        width: 120px;
        height: 120px;
    }

    .image-item-card /deep/ .el-card__body {
        min-height: 160px;
        padding: 20px;
    }

    .actions-container {
        max-width: 1600px;
        gap: 32px;
    }

    .action-btn {
        min-width: 160px;
        height: 52px;
        font-size: 18px;
    }
}
</style>