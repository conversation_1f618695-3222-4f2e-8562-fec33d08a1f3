<template>
  <div class="container-receive">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form ref="refSearchForm">
        <el-row>审核流程</el-row>
        <el-row type="flex">
          <!-- 工作流 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="工作流">
              <el-select @change="changeProcKey" filterable v-model="searchFormData.procKey" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option v-for="item in procKeyList" :key="item.procKey" :label="item.procKeyName" :value="item.procKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 是否被驳回 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否被驳回">
              <el-select v-model="searchFormData.rejectModifyStatus" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="否" :value="0"></el-option>
                <el-option label="是" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品业务编码">
              <el-input v-model="searchFormData.outProductCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 批量类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="searchFormData.procKey === 'meBatchModify'">
            <el-form-item label="批量类型">
              <el-select v-model="searchFormData.batchType" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="基础信息" :value="1"></el-option>
                <el-option label="sku信息" :value="25"></el-option>
                <el-option label="税率" :value="14"></el-option>
                <el-option label="标签信息" :value="24"></el-option>
                <el-option label="扩展信息" :value="2"></el-option>
                <el-option label="用药指导" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">发起人</el-row>
        <el-row v-show="!isFold">
          <!-- 发起人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起人">
              <el-input v-model="searchFormData.taskCreater"></el-input>
            </el-form-item>
          </el-col>
          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <div class="pop-tip" v-show="showTip">{{ nameStr }}</div>
              <el-input
                @mouseover.native="showTip1"
                @mouseleave.native="hideTip"
                v-model="nameStr"
                @click.native="
                  showTree = !showTree
                  isShowPeople = false
                "
                readonly
                clearable
              >
                <el-button
                  slot="append"
                  :icon="showTree ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  @click.stop="
                    showTree = !showTree
                    isShowPeople = false
                  "
                ></el-button>
              </el-input>
              <el-tree v-if="showTree" class="tree-box" :data="deptList" :props="defaultProps" :default-expanded-keys="['1']" node-key="lev">
                <span slot-scope="{ data }">
                  <span class="line mr10">{{ data.dptName }}</span>
                  <el-link v-if="+data.lev > 1" type="primary" class="lh40" @click="showPeople(data.dptCode)">选人</el-link>
                </span>
              </el-tree>
              <div class="people-box" v-if="isShowPeople">
                <el-select
                  ref="autoSelect"
                  multiple
                  collapse-tags
                  v-model="currentPeopleList"
                  placeholder="请选择"
                  @remove-tag="removeTag1"
                  @change="changeSelect1"
                >
                  <el-option label="全选" value="全选" @click.native="selectAll1"></el-option>
                  <el-option v-for="item in peopleList" :key="item.oaId" :label="item.realname" :value="item.oaId"></el-option>
                </el-select>
                <div style="margin-top: 130px; text-align: center">请展开下拉框选人</div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 发起时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="searchFormData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 角色类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="角色类型">
              <el-select filterable v-model="searchFormData.taskCreateRoleCode" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option v-for="item in roleList" :key="item.roleCode" :label="item.roleName" :value="item.roleCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">任务</el-row>
        <el-row>
          <!-- 任务来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务来源">
              <el-select filterable :value="searchFormData.taskSourceList" placeholder="请选择" multiple @input="changeTaskList">
                <el-option label="全部" value="xxx"></el-option>
                <el-option v-for="item in sourceList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品编码 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="searchFormData.productCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input v-model="searchFormData.generalName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="searchFormData.approvalNo"></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="searchFormData.manufacturer"></el-input>
            </el-form-item>
          </el-col>
          <!-- 版本号 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="版本号">
              <el-input v-model="searchFormData.pictureVersion"></el-input>
            </el-form-item>
          </el-col>
          <!-- 商品类型 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品类型">
              <el-select v-model="searchFormData.productType" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="主商品" :value="1"></el-option>
                <el-option label="副商品" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input v-model="searchFormData.smallPackageCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 规格 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格">
              <el-input v-model="searchFormData.spec"></el-input>
            </el-form-item>
          </el-col>
          <!-- 最后更新时间 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="最后更新时间">
              <el-date-picker
                v-model="searchFormData.updateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品业务编码">
              <el-input v-model="searchFormData.outProductCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="isFold = !isFold">展开/收起</el-button>
            <el-button type="primary" size="medium" @click="exportList">导出</el-button>
            <el-button type="primary" size="medium" @click="btnSearchClick(1)">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- table -->
    <div class="table-wrap">
      <el-button v-if="showPreview" type="primary" size="medium" @click="prewviewClick" style="margin-bottom: 15px">预览</el-button>
      <vxe-table
        border
        highlight-hover-row
        resizable
        :height="showPreview ? '90%' : '100%'"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (searchFormData.pageNum - 1) * searchFormData.pageSize }"
        ref="refVxeTable"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
        :default-sort="{ prop: 'createTimeStr', order: 'descending' }"
        :sort-config="{ remote: true }"
        @sort-change="sortChange"
      >
        <vxe-table-column v-if="showPreview" type="checkbox" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column field="procKey" title="工作流" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="batchTypeName" title="批量类型" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="taskNodeName" title="当前审核节点" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          prop="createTimeStr"
          sortable
          field="createTimeStr"
          title="发起时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="taskCreator" title="发起人" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="taskPostCode" title="所属机构" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="taskCreatorRoleCode" title="角色类型" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="source" title="任务来源" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="updateTimeStr" title="最后更新时间" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productId" title="商品ID" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="preOperateStatus" title="是否自营" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="pictureVersion" title="版本号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productTypeName" title="商品类型" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="操作" width="120" show-header-overflow show-overflow fixed="right">
          <template v-slot="{ row }">
            <span v-if="row.procKey === '商品新增流程' || row.procKey === '副商品新增流程'">
              <el-link :underline="false" type="primary" @click.stop="openDetail(row, 0)">查看</el-link>
            </span>
            <span style="margin-left: 5px">
              <el-link :underline="false" type="primary" @click.stop="openDetail(row, 1)">{{ row.hasDownLoad ? "处理" : "下载" }}</el-link>
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="searchFormData.pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="searchFormData.pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>

    <!-- 预览弹框 -->
    <el-dialog
      style="padding: 0"
      class="search-form-wrap"
      fullscreen
      title="预览"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :before-close="clearComment"
    >
      <el-form>
        <el-row>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="驳回原因">
              <el-input v-model="comment" placeholder="请输入驳回理由" @input="changeAllComment"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <vxe-table
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData1"
        :seq-config="{ startIndex: (searchFormData1.pageNum - 1) * searchFormData1.pageSize }"
        ref="previewTable"
        @checkbox-all="selectChangeEvent1"
        @checkbox-change="selectChangeEvent1"
        :editConfig="{ trigger: 'click', mode: 'cell', activeCellMethod: true }"
      >
        <vxe-table-column type="checkbox" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column
          :editRender="{ name: 'input', events: { input: changeComment } }"
          field="comment"
          title="请输入驳回理由"
          min-width="160"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="procKeyName" title="工作流" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuName" title="商品名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturer" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCode" title="spu编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuCode" title="sku编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productType" title="商品类型" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="pictureVersion" title="版本号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          field="bindInstitutionsQuantity"
          title="绑定机构数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="saleChannel" title="销售渠道" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="是否线下业务" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span>{{ row.offlineBusinessType ? "是" : "否" }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click=";(dialogFormVisible = false), (comment = '')">取消预览</el-button>
        <el-button type="danger" @click="reject">批量驳回</el-button>
        <el-button v-if="showPassBtn" type="primary" @click="pass">批量通过</el-button>
      </div>
    </el-dialog>

    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible1"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态" prop="value">
          <el-radio-group v-model="reviewForm.value" @change="statuChange">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="原因" prop="reviewOpinion">
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.reviewOpinion"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible1 = false">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 批量修改审核弹框 -->
    <el-dialog
      title="审核批量文件"
      width="1000px"
      :close-on-click-modal="false"
      :visible.sync="showBatchDialog"
      :close-on-press-escape="false"
      :show-close="false"
      @close="
        batchCommit = ''
        showBatchDialog = false
      "
    >
      <el-form>
        <el-form-item label="驳回原因">
          <el-input
            type="textarea"
            placeholder="驳回必填，超过100的长度，不能提交"
            v-model="batchCommit"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <vxe-table
        height="500"
        border
        v-if="showBatchType === 2"
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="tableData2"
      >
        <vxe-table-column title="序号" width="60" show-header-overflow show-overflow fixed="left">
          <template v-slot="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="processName" title="工作流" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sixCategory" title="六级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="businessScope" title="经营范围" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="inRate" title="进项税率" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="outRate" title="销项税率" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
      <vxe-table
        height="500"
        border
        v-if="showBatchType === 3"
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="tableData3"
      >
        <vxe-table-column title="序号" width="60" show-header-overflow show-overflow fixed="left">
          <template v-slot="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="processName" title="工作流" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuCode" title="sku编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCode" title="spu编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturer" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="一致性评价品种" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ row.conEvaluateVariety | filterConEvaluateVariety }}
          </template>
        </vxe-table-column>
        <vxe-table-column title="医院品种" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ row.hospitalVariety | filterConEvaluateVariety }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="chronicDiseasesVariety" title="慢性病种" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ row.chronicDiseasesVariety | filterConEvaluateVariety }}
          </template>
        </vxe-table-column>
      </vxe-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showBatchDialog = false">取 消</el-button>
        <el-button v-if="showBatchType === 9999" @click="reDownLoad">重新下载文件</el-button>
        <el-button type="danger" @click="rejectBatch">驳回</el-button>
        <el-button type="primary" @click="passBatch">通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { utils_export_excel } from "@/utils"
import { fileDownLoad } from "@/api/productPicture"
import {
  userApprovalProcessList,
  userRoleList,
  taskSourceList,
  myFinishProcessList,
  exportMyFinishProcessList,
  batchPreview,
  batchAuditReject,
  batchAuditPass,
  auditPass,
  auditRejectEnd,
  getOrganizeList,
  getEmployee,
  getBatchDetail,
  batchDownloadDocheck,
} from "@/api/workManage"
let setTime = 0
let tt = false
export default {
  name: "",
  props: {},
  data() {
    return {
      showTip: false,
      isShowPeople: false,
      peopleList: [],
      currentPeopleList: [],
      nameList: [],
      showTree: false,
      comment: "", //批量驳回原因
      dialogFormVisible: false,
      dialogFormVisible1: false,
      reviewForm: {
        value: "",
        reviewOpinion: "",
      },
      rules: {
        value: [
          {
            required: true,
            message: "请选择审核状态",
            trigger: "change",
          },
        ],
        reviewOpinion: [
          {
            required: false,
            message: "请输入审核意见",
            trigger: "blur",
          },
        ],
      },
      currentItem: {},
      showPreview: false, //是否显示预览按钮
      showPassBtn: true, //是否显示通过按钮
      isFold: true,
      searchFormData: {
        outProductCode: '',
        procKey: null, //工作流
        rejectModifyStatus: null, //是否被驳回
        batchType: null, //批量类型
        createTime: [], //发起时间
        taskCreater: "", //发起人
        taskPostCodeList: [], //所属机构 入参
        taskCreateRoleCode: null, //角色类型
        taskSourceList: [], //任务来源
        updateTime: [], //最后更新时间
        generalName: "", //通用名
        approvalNo: "", //批准文号
        manufacturer: "", //生产厂家
        pictureVersion: "", //版本号
        productType: null, //商品类型
        spec: "", //规格
        smallPackageCode: "", //小包装条码
        productCode: "", //商品编码
        createTimeSort: "asc", //排序
        outProductCode: '',
        pageSize: 20,
        pageNum: 1,
      },
      searchFormData1: {
        pageSize: 20,
        pageNum: 1,
      },
      procKeyList: [], //工作流列表
      roleList: [], //角色类型列表
      sourceList: [], //任务来源列表
      deptList: [], //所属机构列表
      tableLoading: false,
      tableData: [],
      tableData1: [], //预览的表格
      tableData2: [], //批量修改税率详情表格
      tableData3: [], //批量修改标签属性详情表格
      checkedList: [],
      checkedList1: [],
      total: 0,
      showBatchDialog: false,
      batchCommit: "",
      showBatchType: null,
    }
  },
  computed: {
    nameStr() {
      return this.nameList.join()
    },
  },
  filters: {
    // 格式化一致性评价品种 格式化医院品种 格式化慢性病种
    filterConEvaluateVariety(e) {
      switch (e) {
        case -1:
          return "无"
        case 0:
          return "否"
        case 1:
          return "是"
      }
    },
  },

  watch: {},
  created() {
    if (this.$route.query.tab) {
      let queryString = window.localStorage.getItem("mytasks")
      if (queryString) {
        this.searchFormData = JSON.parse(queryString)
        this.searchFormData1 = {
          pageSize: this.searchFormData.pageSize,
          pageNum: this.searchFormData.pageNum,
        }
      } else {
        window.localStorage.setItem("mytasks")
      }
    }

    this.getUserApprovalProcessList()
    this.getUserRoleList()
    this.getTaskSourceList()
    this.getDeptList()
    this.btnSearchClick()
  },
  methods: {
    changeTaskList(val) {
      console.log(val);

      if (val.some(str => str == 'xxx') && this.searchFormData.taskSourceList.every(str => str != 'xxx')) {
        this.searchFormData.taskSourceList = ["xxx"];
      } else if (this.searchFormData.taskSourceList.some(str => str == 'xxx') && val.some(str => str == 'xxx')) {
        this.searchFormData.taskSourceList = val.filter(str => str != 'xxx');
      } else {
        this.searchFormData.taskSourceList = val;
      }
    },
    // 修改工作流查询条件
    changeProcKey(e) {
      if (e !== "meBatchModify") {
        this.searchFormData.batchType = null
      }
    },
    showTip1() {
      tt = true
      clearTimeout(setTime)
      this.showTip = true
    },
    hideTip() {
      this.showTip = false
      /* tt = false
      setTime = setTimeout(() => {
        if (!tt) {
          this.showTip = false
          tt = false
        }
      }, 1500) */
    },
    async showPeople(e) {
      try {
        this.currentPeopleList = this.currentPeopleList.filter((item) => {
          return item !== "全选"
        })
        this.isShowPeople = true
        const res = await getEmployee(e)
        this.peopleList = res.data
        this.$refs.autoSelect.toggleMenu()
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        this.currentPeopleList = []
        temp.map((item) => {
          this.currentPeopleList.push(this.peopleList[item].oaId)
        })
        if (this.currentPeopleList.length === this.peopleList.length) {
          this.currentPeopleList.unshift("全选")
        }
      } catch (error) {
        console.log(error)
      }
    },
    sortChange({ column, property, order }) {
      this.searchFormData.createTimeSort = order || "asc"
      this.btnSearchClick(1)
    },
    // 单条修改驳回原因
    changeComment({ row, rowIndex, column }) {
      this.tableData1[rowIndex].comment = column.model.value
      this.checkedList1.map((item) => {
        if (item.applyCode === row.applyCode) {
          item.comment = column.model.value
        }
      })
    },
    // 批量修改驳回理由
    changeAllComment(e) {
      this.tableData1.map((item) => {
        item.comment = e
      })
      this.checkedList1.map((item) => {
        item.comment = e
      })
    },
    // 表格单选 表格全选
    selectChangeEvent({ checked, records }) {
      this.checkedList = []
      records.map((item) => {
        this.checkedList.push({
          applyCode: item.applyCode,
          procInstId: item.procInstId,
          procKeyId: item.procKeyId,
          procKeyName: item.procKeyName,
          productCode: item.productCode,
          productType: item.productTypeName,
          taskId: item.taskId,
        })
      })
    },
    // 表格单选 表格全选(预览弹框)
    selectChangeEvent1({ checked, records }) {
      console.log(records)
      this.checkedList1 = []
      records.map((item) => {
        this.checkedList1.push({
          comment: item.comment,
          applyCode: item.applyCode,
          procInstId: item.procInstId,
          procKey: item.procKeyName,
          taskId: item.taskId,
        })
      })
    },
    // 清空驳回原因
    clearComment() {
      this.comment = ""
      this.dialogFormVisible = false
    },
    // 批量驳回
    reject() {
      if (this.checkedList1.length) {
        let param = _.cloneDeep(this.checkedList1)
        let i = 0
        param.map((item) => {
          if (item.comment) {
            i++
          }
        })
        console.log(param)
        // return
        try {
          if (i !== param.length) {
            this.$message.error("驳回原因不能为空")
            return
          }
          let procKey = ""
          if (this.checkedList1[0].procKeyId === "meSauAdd") {
            procKey = "副商品新增"
          } else if (this.checkedList1[0].procKeyId === "meProductAdd") {
            procKey = "商品新增"
          } else if (this.checkedList1[0].procKeyId === "meProductPresent") {
            procKey = "新品上报"
          } else if (this.checkedList1[0].procKeyId === "meProductPictureEnable") {
            procKey = "精修图版本停启用"
          }
          this.$confirm(`请确认是否批量驳回${this.checkedList1.length}个${procKey}申请？`, "批量驳回", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          })
            .then(async () => {
              let res = await batchAuditReject(param)
              if (res.retCode === 0) {
                console.log(res)
                this.comment = ""
                this.dialogFormVisible = false
                this.btnSearchClick()
                this.$message.success("批量驳回成功")
              } else {
                this.$message.error(res.retMsg)
              }
            })
            .catch(() => {})
        } catch (error) {
          console.log(error)
        }
      } else {
        this.$message.error("请选择需要批量操作的任务！")
      }
    },
    //批量通过
    pass() {
      console.log(this.checkedList1)
      this.checkedList1.map((item) => {
        item.comment = ""
      })
      if (this.checkedList1.length) {
        // return
        try {
          let procKey = ""
          if (this.checkedList1[0].procKeyId === "meProductAdd") {
            procKey = "商品新增"
          } else if (this.checkedList1[0].procKeyId === "meProductPresent") {
            procKey = "新品上报"
          } else if (this.checkedList1[0].procKeyId === "meProductPictureEnable") {
            procKey = "精修图版本停启用"
          }
          this.$confirm(`请确认是否批量通过${this.checkedList1.length}个${procKey}申请？`, "批量通过", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          })
            .then(async () => {
              let res = await batchAuditPass(this.checkedList1)
              if (res.retCode === 0) {
                console.log(res)
                this.comment = ""
                this.dialogFormVisible = false
                this.btnSearchClick()
                this.$message.success("批量通过成功")
              } else {
                this.$message.error(res.retMsg)
              }
            })
            .catch(() => {})
        } catch (error) {
          console.log(error)
        }
      } else {
        this.$message.error("请选择需要批量操作的任务！")
      }
    },
    // 打开批处理弹框
    async prewviewClick() {
      console.log("预览")
      if (this.checkedList.length) {
        this.tableData1 = []
        this.dialogFormVisible = true
        try {
          let res = await batchPreview(this.checkedList)
          if (res.retCode === 0) {
            console.log(res)
            this.tableData1 = res.data
            setTimeout(() => {
              this.$refs.previewTable.setAllCheckboxRow(true)
              this.checkedList1 = this.$refs.previewTable.getCheckboxRecords()
            }, 500)
          } else {
            this.$message.error(res.retMsg)
          }
        } catch (error) {
          console.log(error)
        }
      } else {
        this.$message.error("请选择需要预览的任务！")
      }
      this.checkedList = []
      this.$refs.refVxeTable.clearCheckboxRow()
    },
    // 获取工作流列表
    async getUserApprovalProcessList() {
      try {
        const res = await userApprovalProcessList()
        if (res.retCode === 0) {
          this.procKeyList = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取角色类型列表
    async getUserRoleList() {
      try {
        const res = await userRoleList()
        if (res.retCode === 0) {
          this.roleList = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取任务来源列表
    async getTaskSourceList() {
      try {
        const res = await taskSourceList()
        this.searchFormData.taskSourceList = [];
        if (res.retCode === 0) {
          this.sourceList = res.data
          res.data.forEach(item => {
            if (item.isSelected == 1) {
              this.searchFormData.taskSourceList.push(item.key);
            }
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    changeSelect1(val) {
      // 追加所有已经选中的人
      let temp = []
      let deleteList = []
      let deleteNameList = []
      let tempNameList = []
      this.peopleList.map((item) => {
        if (this.currentPeopleList.indexOf(item.oaId) !== -1) {
          temp.push(item.oaId)
          tempNameList.push(item.realname)
        } else {
          deleteList.push(item.oaId)
          deleteNameList.push(item.realname)
        }
      })
      temp = temp.concat(this.searchFormData.taskPostCodeList)
      this.searchFormData.taskPostCodeList = Array.from(new Set(temp))
      tempNameList = tempNameList.concat(this.nameList)
      this.nameList = Array.from(new Set(tempNameList))
      let deleteIndexList = []
      let deleteNameIndexList = []
      deleteList.map((item) => {
        if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
          deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
        }
      })
      for (let i = deleteIndexList.length - 1; i >= 0; i--) {
        this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
      }
      deleteNameList.map((item) => {
        if (this.nameList.indexOf(item) !== -1) {
          deleteNameIndexList.push(this.nameList.indexOf(item))
        }
      })
      for (let i = deleteNameIndexList.length - 1; i >= 0; i--) {
        this.nameList.splice(deleteNameIndexList[i], 1)
      }
      // 单点到全选
      if (!val.includes("全选") && val.length === this.peopleList.length) {
        this.currentPeopleList.unshift("全选")
      } else if (val.includes("全选") && val.length - 1 < this.peopleList.length) {
        //全选点到不全选
        // 当前下拉框移除全选
        this.currentPeopleList = this.currentPeopleList.filter((item) => {
          return item !== "全选"
        })
      }
    },
    // 下拉框移除值
    removeTag1(val) {
      if (val === "全选") {
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        for (let i = temp.length; index >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(temp[i], 1)
          this.nameList.splice(temp[i], 1)
        }
        this.currentPeopleList = []
      } else {
        this.searchFormData.taskPostCodeList.map((item, i) => {
          if (item === val) {
            this.searchFormData.taskPostCodeList.splice(i, 1)
            this.nameList.splice(i, 1)
          }
        })
      }
    },
    selectAll1() {
      let temp = []
      // 全选
      if (this.currentPeopleList.length < this.peopleList.length) {
        this.currentPeopleList = []
        this.searchFormData.taskPostCodeList = []
        this.peopleList.map((item) => {
          temp.push(item.realname)
          this.currentPeopleList.push(item.oaId)
          this.searchFormData.taskPostCodeList.push(item.oaId)
        })
        this.currentPeopleList.unshift("全选")
        this.searchFormData.taskPostCodeList = Array.from(new Set(this.searchFormData.taskPostCodeList))
        this.nameList = this.nameList.concat(temp)
        this.nameList = Array.from(new Set(this.nameList))
        // this.searchFormData.taskPostCodeList.unshift("全选")
      } else {
        //取消全选
        this.currentPeopleList.shift()
        this.peopleList.map((item) => {
          for (let index = this.currentPeopleList.length - 1; index >= 0; index--) {
            if (this.currentPeopleList[index] === item.oaId) {
              temp.push(item.oaId)
              this.currentPeopleList.splice(index, 1)
              this.nameList.splice(index, 1)
            }
          }
        })
        let deleteIndexList = []
        temp.map((item) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
            deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
          }
        })
        for (let i = deleteIndexList.length - 1; i >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
        }
      }
    },
    // 获取所属机构列表
    async getDeptList() {
      try {
        const res = await getOrganizeList()
        this.deptList = res.data
      } catch (error) {
        console.log(error)
      }
    },
    async getListData() {
      let param = Object.assign({}, this.searchFormData)
      console.log(param)
      if (param.createTime && param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if (param.updateTime && param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      if (param.taskPostCodeList[0] === "全选") {
        param.taskPostCodeList.shift()
      }
      param.taskSourceList = param.taskSourceList.filter(str => str != "xxx");
      delete param.createTime
      delete param.updateTime
      console.log(param)
      try {
        const res = await myFinishProcessList(param)
        if (res.retCode === 0) {
          this.tableData = res.data.list
          this.total = res.data.total
          if (
            param.procKey === "meSauAdd" ||
            param.procKey === "meProductPictureEnable" ||
            param.procKey === "meProductPresent" ||
            (param.procKey === "meProductAdd" && param.productType === 0)
          ) {
            this.showPreview = true
            this.showPassBtn = param.procKey === "meProductPresent" ? false : true
          } else {
            this.showPreview = false
            this.checkedList = []
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 导出
    exportList() {
      let param = Object.assign({}, this.searchFormData)
      if (param.createTime && param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if (param.updateTime && param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      if (param.taskPostCodeList[0] === "全选") {
        param.taskPostCodeList.shift()
      }
      param.taskSourceList = param.taskSourceList.filter(str => str != "xxx");
      delete param.createTime
      delete param.updateTime
      delete param.pageNum
      delete param.pageSize
      console.log(param)
      exportMyFinishProcessList(param)
        .then((res) => {
          if (!res.retCode) {
            this.$message.success(res.retMsg)
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            })
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          })
        })
    },
    btnSearchClick(e) {
      if (e) {
        this.searchFormData.pageNum = 1
      }
      this.getListData()
    },
    btnResetClick() {
      this.nameList = []
      this.currentPeopleList = []
      this.searchFormData = {
        procKey: null, //工作流
        rejectModifyStatus: null, //是否被驳回
        batchType: null, //批量类型
        createTime: [], //发起时间
        taskCreater: "", //发起人
        taskPostCodeList: [], //所属机构 入参
        taskCreateRoleCode: null, //角色类型
        taskSourceList: [], //任务来源
        updateTime: [], //最后更新时间
        generalName: "", //通用名
        approvalNo: "", //批准文号
        manufacturer: "", //生产厂家
        productType: null, //商品类型
        pictureVersion: "", //版本号
        spec: "", //规格
        smallPackageCode: "", //小包装条码
        createTimeSort: "asc", //排序
        productCode: "", //商品编码
        pageSize: this.searchFormData.pageSize,
        pageNum: 1,
      }
      ;(this.total = 0), this.btnSearchClick()
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.searchFormData.pageNum = 1
      this.searchFormData.pageSize = pageSize
      this.getListData()
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      console.info(currentPage)
      this.searchFormData.pageNum = currentPage
      this.getListData()
    },
    statuChange(e) {
      if (e) {
        this.rules.reviewOpinion[0].required = false
      } else {
        this.rules.reviewOpinion[0].required = true
      }
    },
    dialogClose() {
      this.reviewForm = {
        value: "",
        reviewOpinion: "",
      }
      this.rules.reviewOpinion[0].required = false
      this.currentItem = {}
    },
    save() {
      this.$refs.reviewForm.validate(async (valid) => {
        if (valid) {
          let param = {
            applyCode: this.currentItem.applyCode,
            comment: this.reviewForm.reviewOpinion,
            procInstId: this.currentItem.procInstId,
            procKey: this.currentItem.procKey,
            taskId: this.currentItem.taskId,
          }
          try {
            let res = null
            if (this.reviewForm.value == 1) {
              res = await auditPass(param)
            } else {
              res = await auditRejectEnd(param)
            }
            console.log(res)
            if (res.retCode === 0) {
              this.dialogFormVisible1 = false
              this.currentItem = {}
              this.btnSearchClick(1)
            } else {
              this.$message.error(res.retMsg)
            }
          } catch (error) {
            console.log(error)
          }
        }
      })
    },
    // 批量修改驳回
    async rejectBatch() {
      try {
        if (!this.batchCommit) {
          this.$message.error("驳回原因必填")
          return
        }
        let param = {
          applyCode: this.currentItem.applyCode,
          comment: this.batchCommit,
          procInstId: this.currentItem.procInstId,
          procKey: this.currentItem.procKey,
          taskId: this.currentItem.taskId,
        }
        const res = await auditRejectEnd(param)
        this.showBatchDialog = false
        this.currentItem = {}
        this.batchCommit = ""
        this.btnSearchClick(1)
        this.$message.success(res.retMsg)
      } catch (error) {
        console.log(error)
      }
    },
    // 批量修改通过
    async passBatch() {
      try {
        let param = {
          applyCode: this.currentItem.applyCode,
          comment: this.batchCommit,
          procInstId: this.currentItem.procInstId,
          procKey: this.currentItem.procKey,
          taskId: this.currentItem.taskId,
        }
        const res = await auditPass(param)
        this.showBatchDialog = false
        this.currentItem = {}
        this.batchCommit = ""
        this.btnSearchClick(1)
        this.$message.success(res.retMsg)
      } catch (error) {
        console.log(error)
      }
    },
    // 重新下载文件
    async reDownLoad() {
      const res = await fileDownLoad(this.currentDownload.fileDownLoadAddress, this.currentDownload.fileName)
      utils_export_excel(res, this.currentDownload.fileName)
      await batchDownloadDocheck({ applyCode: this.currentItem.applyCode, taskNode: this.currentItem.taskNode })
      this.showBatchDialog = false
      this.batchCommit = ""
      this.searchFormData(1)
    },
    // 查看详情
    async openDetail(e, t) {

      console.log(e)
      let { procDefId, taskNode, procInstId, productCode, applyCode, taskPostCode, source, outProductCode } = e
      if (!t) {
        //查看
        try {
          parent.CreateTab(
            "../static/dist/index.html#/workManage/" +
              (e.procKey === "商品新增流程" ? "addGoodsDetail" : "addDeputyDeal") +
              "?procInstId=" +
              procInstId +
              "&procKey=" +
              e.procKey +
              "&procDefId=" +
              procDefId +
              "&nodeKey=" +
              taskNode +
              "&taskPostCode=" +
              taskPostCode +
              "&source=" +
              source +
              "&applyCode=" +
              applyCode +
              "&productType=" +
              2 +
              "&productCode=" +
              productCode +
              "&outProductCode=" +
              outProductCode +
              "&detailType=self" +
              "&pageType=detail",
            "任务详情"
          )
        } catch (error) {
          this.$router.push({
            path: e.procKey === "商品新增流程" ? "/workManage/addGoodsDetail" : "/workManage/addDeputyDeal",
            query: {
              procInstId,
              nodeKey: taskNode,
              procDefId,
              applyCode,
              procKey: e.procKey,
              productType: 2,
              productCode,
              detailType: "self",
              taskPostCode,
              outProductCode,
              pageType: "detail",
              source,
            },
          })
        }
      } else {
        let query = Object.assign({}, this.searchFormData, this.searchFormData1)
        let storeStr = JSON.stringify(query)
        window.localStorage.setItem("mytasks", storeStr)

        //处理
        if (e.procKey === "新品上报流程") {
          console.log("新品上报流程")
          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/newGoodsReportDeal?uniqueCode=" +
                e.uniqueCode +
                "&procInstId=" +
                procInstId +
                "&procKey=" +
                e.procKey +
                "&taskId=" +
                e.taskId +
                "&procDefId=" +
                procDefId +
                "&taskPostCode=" +
                taskPostCode +
                "&outProductCode=" +
                outProductCode +
                "&source=" +
                source +
                "&applyCode=" +
                applyCode +
                "&nodeKey=" +
                taskNode +
                "&type=present",
              "商品新增",
              true
            )
          } catch (error) {
            console.error(error)
            this.$router.push({
              name: "newGoodsReportDeal",
              query: {
                procInstId,
                nodeKey: taskNode,
                procDefId,
                outProductCode,
                // uniqueCode:"ZHL15870286-d6792ce884",
                uniqueCode: e.uniqueCode,
                procKey: e.procKey,
                taskId: e.taskId,
                type: "present",
                taskPostCode,
                source,
                applyCode,
              },
            })
          }
        } else if (e.procKey === "商品新增流程" || e.procKey === "副商品新增流程" || e.procKey === "预首营审核" || e.procKey === "商品修改") {
          // 商品新增流程 预首营审核
          console.log("商品新增流程 预首营审核")

          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/" +
                (e.procKey === "副商品新增流程" || (e.procKey === "商品修改" && e.productTypeName === "副商品")
                  ? "addDeputyDeal"
                  : "addGoodsDeal") +
                "?productCode=" +
                productCode +
                "&productType=" +
                2 +
                "&applyCode=" +
                applyCode +
                "&modify=" +
                e.modify +
                "&procKey=" +
                e.procKey +
                "&nodeKey=" +
                taskNode +
                "&taskId=" +
                e.taskId +
                "&procDefId=" +
                procDefId +
                "&outProductCode=" +
                outProductCode +
                "&taskPostCode=" +
                taskPostCode +
                "&source=" +
                source +
                "&procInstId=" +
                procInstId +
                "&detailType=self" +
                "&pageType=edit",
              "商品审核明细页"
            )
          } catch {
            this.$router.push({
              path:
                e.procKey === "副商品新增流程" || (e.procKey === "商品修改" && e.productTypeName === "副商品")
                  ? "/workManage/addDeputyDeal"
                  : "/workManage/addGoodsDeal",
              query: {
                procDefId,
                procInstId,
                nodeKey: taskNode,
                procKey: e.procKey,
                taskId: e.taskId,
                productCode: productCode,
                outProductCode,
                productType: 2,
                taskPostCode,
                source,
                applyCode: applyCode,
                pageType: "edit",
                detailType: "self",
                modify: e.modify,
              },
            })
          }
        } else if (e.procKey === "修改精修图流程" && e.taskNodeName === "运营一审") {
          // 一审
          console.log("一审")
          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/finishingDeal?productCode=" +
                productCode +
                "&productType=" +
                2 +
                "&applyCode=" +
                applyCode +
                "&procKey=" +
                e.procKey +
                "&nodeKey=" +
                taskNode +
                "&outProductCode=" +
                outProductCode +
                "&taskId=" +
                e.taskId +
                "&procDefId=" +
                procDefId +
                "&taskPostCode=" +
                taskPostCode +
                "&source=" +
                source +
                "&taskNode=" +
                taskNode +
                "&procInstId=" +
                procInstId,
              "改精修图-处理"
            )
          } catch {
            this.$router.push({
              name: "finishingDeal",
              query: {
                procDefId,
                procInstId,
                nodeKey: taskNode,
                procKey: e.procKey,
                taskId: e.taskId,
                outProductCode,
                productCode: productCode,
                productType: 2,
                taskPostCode,
                source,
                taskNode,
                applyCode: applyCode,
              },
            })
          }
        } else if (e.procKey === "修改精修图流程" && e.taskNodeName === "运营二审") {
          // 运营二审
          console.log("运营二审")
          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/finishingAudit?productCode=" +
                productCode +
                "&productType=" +
                2 +
                "&applyCode=" +
                applyCode +
                "&procKey=" +
                e.procKey +
                "&nodeKey=" +
                taskNode +
                "&outProductCode=" +
                outProductCode +
                "&taskId=" +
                e.taskId +
                "&procDefId=" +
                procDefId +
                "&taskPostCode=" +
                taskPostCode +
                "&source=" +
                source +
                "&taskNode=" +
                taskNode +
                "&procInstId=" +
                procInstId,
              "改精修图-审核"
            )
          } catch {
            this.$router.push({
              name: "finishingAudit",
              query: {
                procDefId,
                procInstId,
                nodeKey: taskNode,
                procKey: e.procKey,
                taskId: e.taskId,
                outProductCode,
                productCode: productCode,
                productType: 2,
                taskPostCode,
                source,
                taskNode,
                applyCode: applyCode,
              },
            })
          }
        } else if (e.procKey === "商品合并流程") {
          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/mergeGoodsDeal?productCode=" +
                productCode +
                "&productType=" +
                2 +
                "&applyCode=" +
                applyCode +
                "&procKey=" +
                e.procKey +
                "&taskId=" +
                e.taskId +
                "&outProductCode=" +
                outProductCode +
                "&procDefId=" +
                procDefId +
                "&taskPostCode=" +
                taskPostCode +
                "&source=" +
                source +
                "&procInstId=" +
                procInstId +
                "&detailType=edit",
              "商品审核明细页"
            )
          } catch {
            this.$router.push({
              name: "mergeGoodsDeal",
              query: {
                procInstId,
                procDefId,
                procKey: e.procKey,
                taskId: e.taskId,
                taskPostCode,
                outProductCode,
                source,
                productCode: productCode,
                productType: 2,
                applyCode: applyCode,
                detailType: "edit",
              },
            })
          }
        } else if (e.procKey === "商品取消合并流程") {
          this.currentItem = e
          this.dialogFormVisible1 = true
        } else if (e.procKey === "精修图版本停启用") {
          console.log("精修图版本停启用")
          try {
            parent.CreateTab(
              "../static/dist/index.html#/workManage/imgVersionDeal?productCode=" +
                productCode +
                "&productType=" +
                2 +
                "&applyCode=" +
                applyCode +
                "&procKey=" +
                e.procKey +
                "&outProductCode=" +
                outProductCode +
                "&taskId=" +
                e.taskId +
                "&procDefId=" +
                procDefId +
                "&taskPostCode=" +
                taskPostCode +
                "&source=" +
                source +
                "&procInstId=" +
                procInstId,
              "精修图停启用审核明细页"
            )
          } catch {
            this.$router.push({
              name: "imgVersionDeal",
              query: {
                procInstId,
                procDefId,
                procKey: e.procKey,
                taskId: e.taskId,
                taskPostCode,
                outProductCode,
                source,
                productCode,
                productType: 2,
                applyCode,
              },
            })
          }
        } else if (e.procKey === "批量修改流程") {
          this.currentItem = e
          try {
            const res = await getBatchDetail(e.applyCode)
            console.log(res)
            if (e.batchType === 14) {
              // 批量修改税率
              this.showBatchType = 2
              this.showBatchDialog = true
              this.tableData2 = res.data
            } else if (e.batchType === 24) {
              // 批量修改标签属性
              this.showBatchType = 3
              this.showBatchDialog = true
              this.tableData3 = res.data
            } else {
              // 批量修改基础信息、sku属性、扩展属性、用药指导
              this.currentDownload = res.data
              if (!e.hasDownLoad) {
                // 需先下载
                this.$confirm(res.data.fileName, "下载批量文件", {
                  confirmButtonText: "下载",
                  cancelButtonText: "取消",
                  closeOnClickModal: false,
                })
                  .then(async () => {
                    const res2 = await fileDownLoad(res.data.fileDownLoadAddress, res.data.fileName)
                    utils_export_excel(res2, res.data.fileName)
                    await batchDownloadDocheck({ applyCode, taskNode })
                    this.btnSearchClick(1)
                  })
                  .catch(() => {})
              } else {
                // 无需先下载
                this.showBatchType = 9999
                this.showBatchDialog = true
              }
            }
          } catch (error) {
            console.log(error)
          }
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/**
   * 查询表单
   */
.search-form-wrap {
  width: 100%;
  padding: 15px;
  border-bottom: 1px dashed #e4e4eb;

  .el-row {
    flex-wrap: wrap;

    .el-col {
      display: flex;
      justify-content: flex-end;

      .el-form-item {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;

        /deep/ {
          .el-form-item__label {
            width: 116px;
            line-height: normal;
            padding: 0 12px;
            color: #292933;
            font-weight: normal;
          }

          .el-form-item__content {
            flex: 1;

            .el-range-editor.el-input__inner {
              width: 100%;

              .el-range-separator {
                width: 14px;
                padding: 0;
              }
            }
          }
        }

        .el-input {
          width: 100%;
        }

        .el-select {
          width: 100%;
        }
      }
    }
  }
}
.recieve-wrap {
  padding-top: 15px;
}
.recieve {
  width: 625px;
  padding-top: 50px;
  padding-left: 40px;
  background: #e9f7fe;
  margin: 0 auto;
  .content {
    display: flex;
    align-items: center;
    .title {
      padding-right: 50px;
      color: #3b95a8;
    }
    .btn {
      margin-left: 15px;
    }
  }
  .tip {
    font-size: 12px;
    color: #bababa;
    padding: 40px 0;
  }
}
/**
     * table
     */
.table-wrap {
  width: 100%;
  min-height: 440px;
  height: calc(100vh - 296px);
  padding: 0 15px;
  margin-top: 15px;
}
.tree-box {
  width: 100%;
  height: 40px;
  z-index: 20;
  overflow: auto;
  height: 330px;
  position: absolute;
  top: 60px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.people-box {
  position: absolute;
  left: 100%;
  top: 60px;
  background: #a19f9f;
  width: 100%;
  height: 330px;
  z-index: 20;
}
.mr10 {
  margin-right: 10px;
}
.lh40 {
  /deep/ .el-link--inner {
    line-height: 40px;
  }
}
/deep/ .el-tree-node {
  background: #fff;
  .el-tree-node__content {
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
  }
}
.pop-tip {
  position: absolute;
  z-index: 30;
  top: -110px;
  width: 230%;
  height: 100px;
  overflow: hidden;
  background: #fff;
  border-radius: 5px;
  border: 1px solid #dcdfe6;
  line-height: 20px;
}
</style>
<style lang="scss">
.vxe-table--tooltip-wrapper {
  z-index: 10000 !important;
}
</style>
