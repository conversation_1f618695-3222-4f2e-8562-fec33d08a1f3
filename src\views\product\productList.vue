
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex" :gutter="20">
          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input
                v-model="formData.spuCode"
                placeholder="商品编码/spu/sku/原商品编码/商品id"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="formData.generalName"
                placeholder="通用名/商品名/助记码"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input
                v-model="formData.approvalNo"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input
                v-model="formData.smallPackageCode"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturer"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 规格/型号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格/型号">
              <el-input
                v-model="formData.spec"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 商品类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="商品类型">
              <el-select v-model="formData.type" placeholder="请选择" clearable>
                <el-option label="全部" value></el-option>
                <el-option label="主商品" value="sku"></el-option>
                <el-option label="副商品" value="sau"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 受托生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="受托生产厂家">
              <el-input
                v-model="formData.entrustedManufacturerStr"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="商品大类">
              <el-select
                @change="changeSpuCategory"
                v-model="formData.spuCategory"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 处方分类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="处方分类">
              <el-select
                v-model="formData.prescriptionCategory"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in prescriptionCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 经营范围 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="经营范围">
              <el-cascader
                ref="ownCascader"
                style="width: 100%"
                placeholder="请先选择商品大类"
                v-model="businessScopeList"
                @change="handleChange"
                :disabled="!formData.spuCategory"
                :options="businessScopeListOptions"
                :show-all-levels="true"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'dictName',
                }"
                clearable
              >
                <template slot-scope="{ data }">
                  <span>{{ data.dictName }}</span>
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>
          <!-- 六级分类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="六级分类">
              <el-cascader
                :options="categoryList"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  lazy: true,
                  lazyLoad: lazyLoad,
                }"
                clearable
                v-model="category"
                @change="categoryChange"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <!-- 剂型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="剂型">
              <el-select
                v-model="formData.dosageForm"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in DosageFormList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 自营状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="自营状态">
              <el-select
                v-model="formData.preOperateStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option label="非自营" value="1"></el-option>
                <el-option label="自营" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 停用状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="停用状态">
              <el-select
                v-model="formData.disableStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option label="停用" value="0"></el-option>
                <el-option label="启用" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 停用原因 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="停用原因">
              <el-input
                v-model="formData.disableReason"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 创建时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 最后修改时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="最后修改时间" prop="updateTime">
              <el-date-picker
                v-model="formData.updateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
           <!-- 是否线下业务 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="是否线下业务">
              <el-select v-model="formData.offlineBusinessType" placeholder="请选择" clearable>
                <el-option label="全部" value></el-option>
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 是否含图 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="是否含图">
              <el-select v-model="formData.hasPicture" placeholder="请选择" clearable>
                <el-option label="全部" :value="null"></el-option>
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 产地 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="产地" prop="originPlace">
              <el-input v-model="formData.originPlace" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 品牌 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="品牌" prop="brand"> 
              <el-input v-model="formData.brand" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button
              type="primary"
              size="medium"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button
        v-if="hasPermission('商品列表-新增商品')"
        type="primary"
        size="medium"
        @click="addProduct"
        >新增主商品</el-button
      >
      <!-- <el-button
        v-if="hasPermission('商品列表-新增商品')"
        type="primary"
        size="medium"
        @click="addMainProduct"
        >新增主商品</el-button
      > -->
      <el-button
        v-if="hasPermission('商品列表-新增商品')"
        type="primary"
        size="medium"
        @click="addDeputyProduct"
        >新增副商品</el-button
      >
      <el-button size="medium" @click="handleExport">导出EXCEL</el-button>
      <!-- <excel-file-upload
        v-if="hasPermission('商品列表-新增商品')"
        :scope="{
          applyCode: '',
          disabled: false,
          type: '4',
          buttonType: 'button',
          url: '/api/spu/import/batchAddSpu',
          downloadName: '批量新增模板.xlsx',
          downloadUrl: '../../../static/assets/excel/批量新增模板.xlsx',
        }"
        @success="excelUpload"
        >批量新增sku</excel-file-upload
      > -->
      <excel-file-upload
        :scope="{
          applyCode: '',
          disabled: false,
          type: '4',
          buttonType: 'button',
          url: '/api/spu/import/batchAddDict',
        }"
        @success="excelUpload"
        >导入字典</excel-file-upload
      >
      <excel-file-upload
        v-if="hasPermission('商品列表-新增商品')"
        :scope="{
          applyCode: '',
          disabled: false,
          type: '4',
          buttonType: 'button',
          url: '/api/batchUpload/batchAddFuProduct',
          downloadBatchUrl: true,
          showMsg: true
        }"
        @success="excelUpload"
        >批量新增副商品</excel-file-upload
      >
      <vxe-toolbar custom style="display: inline-block"></vxe-toolbar>
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        highlight-current-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        @sort-change="sortQueryList"
        @cell-dblclick="cellDBLClickEvent"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategory"
          title="商品大类"
          width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="prodType"
          title="商品类型"
          width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.prodType | filterProductType }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="sauCode"
          title="商品编码"
          width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span v-if="row.dataType != -1">{{ row.sauCode }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="skuCode"
          title="sku编码"
          width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            <span v-if="row.dataType != -1">{{ row.skuCode }}</span>
          </template></vxe-table-column
        >
        <vxe-table-column
          field="spuCode"
          title="spuCode"
          width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="originalProductCode"
          title="原商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品id"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="skuName"
          title="商品名"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格型号"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnit"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturer"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="entrustedManufacturerStr"
          title="受托生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="brand"
          title="品牌商标"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="prescriptionCategory"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="businessScopeMultiStr"
          title="所属经营范围"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <!-- <vxe-table-column
          field="tasteName"
          title="口味"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sizeName"
          title="尺码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column> -->
        <vxe-table-column
          field="originPlace"
          title="产地"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="firstCategory"
          title="一级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="secondCategory"
          title="二级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="thirdCategory"
          title="三级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="fourthCategory"
          title="四级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="fiveCategory"
          title="五级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sixCategory"
          title="六级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taxCategoryCode"
          title="税务分类编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="dosageForm"
          title="剂型"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="operateMechanismCount"
          title="已首营机构数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="preOperateStatus"
          title="自营状态"
          min-width="120"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            {{ row.preOperateStatus | filterPreOperateStatus }}
          </template></vxe-table-column
        >
        <vxe-table-column
          field="disableStatus"
          title="停用状态"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="disableReason"
          title="停用原因"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="createUser"
          title="创建人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="创建时间"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ parseTime(row.createTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="createInstitutionName"
          title="创建人机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="updateTime"
          title="最后修改时间"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ parseTime(row.updateTime) }}
          </template></vxe-table-column
        >
        <vxe-table-column
          field="standardCodes"
          title="药品本位码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="offlineBusinessType"
          title="是否线下业务"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            {{ row.offlineBusinessType | filterOfflineBusinessType }}
          </template></vxe-table-column>
        <vxe-table-column
          title="是否含图"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
          {{ row.pictureCount ? '是' : '否'}}
        </template></vxe-table-column>
        <vxe-table-column
          title="操作"
          width="280"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span
              class="btn"
              v-if="
                hasPermission('商品列表-修改商品') &&
                !row.applyCode
              "
            >
              <el-link :underline="false" type="primary" @click.stop="edit(row)"
                >修改</el-link
              >
            </span>
            <span
              class="btn"
              v-if="
                hasPermission('商品列表-修改商品') &&
                !row.applyCode &&
                row.disableStatus == '启用' &&
                row.preOperateStatus == 1
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click.stop="editYSY(row)"
                >预首营</el-link
              >
            </span>
            <span class="btn" v-if="row.reviewStatus == '审核通过'">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="cancelMerge(row)"
                >取消合并</el-link
              >
            </span>
             <span class="btn">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="downstream(row.sauCode)"
                >同步下游</el-link
              >
            </span>
            <span
              class="btn"
              v-if="
                row.reviewStatus == '审核通过' &&
                row.approvalImg + row.outPackageImg + row.directionImg > 0
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click.stop="ZGImgDownLoad(row)"
                >质管图片下载</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <el-dialog
      title="导出字段"
      :visible.sync="dialogVisible"
      width="500"
      :before-close="handleClose"
    >
      <div class="tree-btn">
        <el-button type="primary" size="mini" @click="clearTree"
          >重置</el-button
        >
        <span>（勾选字段不要超出30个）</span>
      </div>
      <div class="tree-wrap">
        <ul id="dictTree" class="ztree" />
      </div>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="sure">确 定</el-button>
    </el-dialog>
  </div>
</template>

<script>
import { parseTimestamp, hasPermission } from "@/utils/index.js";
import {
  getProductList,
  cancelMergeProduct,
  exportProductList,
  downstream,
} from "@/api/product.js";
import { exportProductDictList } from "@/utils/config.js";
import {
  categoryList,
  findDictList,
  getTotalDictionaryTree,
} from "@/api/dict.js";
import excelFileUpload from "@/components/common/excelFileUpload";
export default {
  name: "productList",
  components: { excelFileUpload },
  filters: {},
  props: {},
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      sortFiled: "updateTime desc ,sau.spuCode", // 排序查询
      sortRule: "desc", //(升序-ASC, 降序-DESC)
      formData: {
        reviewStatus: 3,
      },
      tableLoading: false,
      tableData: [],
      categoryList: [],
      spuCategoryList: [],
      isUnfold: false,
      dialogVisible: false,
      businessScopeListOptions: [],
      businessScopeList: [],
      category: [],
      prescriptionCategoryList: [],
      dosageFormList: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm();
    this.getFixCategoryList({
      isValid: 1,
      level: 1,
      parentId: "",
    });
    this.getCategoryList();
    this.getPrescriptionCategoryList();
    this.getDosageFormList();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.sortList = this.sortFiled
          ? [
              {
                order: "0",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [];
        if (param.updateTime && param.updateTime.length > 0) {
          param.updateStartTime = param.updateTime[0];
          param.updateEndTime = param.updateTime[1];
        }
        if (param.createTime && param.createTime.length > 0) {
          param.createStartTime = param.createTime[0];
          param.createEndTime = param.createTime[1];
        }
        delete param.updateTime;
        delete param.createTime;
        if (this.businessScopeList.length > 0) {
          param.businessScopeMulti = this.businessScopeList[
            this.businessScopeList.length - 1
          ];
        }
        let res = await getProductList(param);
        this.tableLoading = false;
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    // 获取六级分类
    getFixCategoryList(data) {
      categoryList(data).then((res) => {
        if (!res.retCode) {
          this.categoryList = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },
    // 获取处方分类
    getPrescriptionCategoryList() {
      findDictList({
        type: 9,
      }).then((res) => {
        this.prescriptionCategoryList = res.data.list;
      });
    },
    // 获取剂型
    getDosageFormList() {
      findDictList({
        type: 5,
      }).then((res) => {
        this.DosageFormList = res.data.list;
      });
    },
    // 动态加载六级分类
    lazyLoad(node, resolve) {
      categoryList({
        isValid: 1,
        level: node.level + 1,
        parentId: node.value,
      }).then((res) => {
        if (!res.retCode) {
          if (node.level == 5) {
            res.data.forEach((item) => {
              item.leaf = 6;
            });
          }
          resolve(res.data);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 六级分类改变
    categoryChange(e) {
      this.formData.firstCategory = e[0] ? e[0] : "";
      this.formData.secondCategory = e[1] ? e[1] : "";
      this.formData.thirdCategory = e[2] ? e[2] : "";
      this.formData.fourthCategory = e[3] ? e[3] : "";
      this.formData.fiveCategory = e[4] ? e[4] : "";
      this.formData.sixCategory = e[5] ? e[5] : "";
    },
    // 商品大类改变获取经营范围配置
    changeSpuCategory(type) {
      this.businessScopeList = [];
      getTotalDictionaryTree({
        type: "11",
        parentId: type,
      }).then((res) => {
        this.businessScopeListOptions = res.data;
      });
    },
    handleChange() {
      this.$refs.ownCascader.dropDownVisible = false;
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      // this.$refs["refSearchForm"].resetFields();
      // this.formData.createTime = "";
      this.formData = {
        spuCategory: "",
        spuCode: "",
        generalName: "",
        smallPackageCode: "",
        preOperateStatus: "",
        spec: "",
        manufacturer: "",
        entrustedManufacturerStr:"",
        approvalNo: "",
        prescriptionCatnegory: "",
        updateStartTime: "",
        updateEndTime: "",
        firstCategory: "",
        secondCategory: "",
        thirdCategory: "",
        fourthCategoryStr: "",
        type: "",
        disableStatus: "",
        disableReason: "",
        hasPicture: null,
      };
      this.category = [];
      this.businessScopeList = [];
      this.$refs.refVxeTable.clearSort();
      this.sortFiled = "spuCode";
      this.sortRule = "desc";
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    // 排序查询
    sortQueryList({ column, property, order }) {
      if (order) {
        this.sortFiled = property;
        this.sortRule = order;
      } else {
        this.sortFiled = "spuCode";
        this.sortRule = "desc";
      }
      this.pageNum = 1;
      this.searchForm();
    },

    receive(row) {
      worksubstitutionReceive({
        applyCode: row.applyCode,
        id: row.id,
        receiveStatus: 1,
      }).then((res) => {
        if (!res.retCode) {
          this.searchForm();
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time);
    },
    hasPermission(str) {
      return hasPermission(str);
    },
    // 新增商品
    addProduct() {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/addProduct",
          "商品新增",
          this
        );
      } catch {
        this.$router.push({
          path: "/product/addProduct",
        });
      }
    },
    // 新增主商品
    addMainProduct() {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/addMainProduct",
          "新增主商品",
          this
        );
      } catch {
        this.$router.push({
          path: "/product/addMainProduct",
        });
      }
    },
    // 新增副商品
    addDeputyProduct() {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/addDeputyProduct",
          "新增副商品",
          this
        );
      } catch {
        this.$router.push({
          path: "/product/addDeputyProduct",
        });
      }
    },
    // 修改商品
    edit(row) {
      let productCode, productType;
      if (row.prodType == 3) {
        productCode = row.sauCode;
        productType = 3;
      } else {
        productCode = row.skuCode;
        productType = 2;
      }
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/"+(productType === 2 ? 'editProduct' : 'addDeputyProduct')+"?productCode=" +
            productCode +
            "&sauCode=" +
            row.sauCode +
            "&preOperateStatus=" +
            row.preOperateStatus +
            "&productType=" +
            productType +
            "&spuCode=" +
            row.spuCode +
            "&pageType=edit",
          "商品修改",
          this
        );
      } catch {
        this.$router.push({
          path: `/product/${productType === 2 ? 'editProduct' : 'addDeputyProduct'}`,
          query: {
            productCode: productCode,
            productId: row.productId,
            productType: productType,
            spuCode: row.spuCode,
            sauCode: row.sauCode,
            preOperateStatus:row.preOperateStatus,
            pageType:"edit",
          },
        });
      }
    },
    // 预首营
    editYSY(row) {
      let productCode, productType;
      if (row.prodType == 3) {
        productCode = row.sauCode;
        productType = 3;
      } else {
        productCode = row.skuCode;
        productType = 2;
      }
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/editProduct?productCode=" +
            productCode +
            "&preOperateStatus=" +
            row.preOperateStatus +
            "&sauCode=" +
            row.sauCode +
            "&id=" +
            row.productId +
            "&productType=" +
            productType +
            "&applyCode=" +
            row.applyCode +
            "&spuCode=" +
            row.spuCode +
            "&detailType=self&from=operate",
          "预首营商品修改"
        );
      } catch {
        this.$router.push({
          path: "/product/editProduct",
          query: {
            productCode: productCode,
            id: row.productId,
            productType: productType,
            applyCode: row.applyCode,
            spuCode: row.spuCode,
            detailType: "self",
            from: "operate",
            sauCode: row.sauCode,
            preOperateStatus:row.preOperateStatus,
          },
        });
      }
    },
    // 批量导入
    excelUpload(data) {
      if (data.response.success) {
        this.$message.success("上传成功");
      }
    },
    // 取消合并
    cancelMerge(row) {
      cancelMergeProduct({
        applyCode: row.applyCode,
        productCode: row.sauCode,
      }).then((res) => {
        if (!res.retCode) {
          window.parent.CreateTab(
            "/api/product/to/cancelMergeDetail?applyCode=" +
              row.applyCode +
              "&productCode=" +
              row.sauCode,
            "商品取消合并详情"
          );
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 质管图片下载
    ZGImgDownLoad(row) {
      let productCode = row.prodType == 3 ? row.sauCode : row.skuCode;
      this.$message.success("开始下载，请耐心等待");
      location.href =
        "/api/spu/download/getProductMedia?productCode=" +
        productCode +
        "&productType=" +
        row.prodType +
        "&spuCode=" +
        row.spuCode;
    },
    cellDBLClickEvent({ row }) {
      var productCode = row.sauCode != row.skuCode ? row.sauCode : row.skuCode;
      var productType = row.sauCode != row.skuCode ? 3 : 2;
      if (row.dataType == -1) {
        try {
          parent.CreateTab(
            "../static/dist/index.html#/product/draftProduct?productCode=" +
              productCode +
              "&productType=" +
              productType +
              "&spuCode=" +
              row.spuCode +
              "&applyCode=" +
              row.applyCode +
              "&dataType=" +
              row.dataType,
            "草稿编辑"
          );
        } catch {
          this.$router.push({
            path: "/product/draftProduct",
            query: {
              productCode: productCode,
              productType: productType,
              spuCode: row.spuCode,
              applyCode: row.applyCode,
              dataType: row.dataType,
            },
          });
        }
      } else {
        try {
          parent.CreateTab(
            "../static/dist/index.html#/product/"+(row.prodType === "2" ? "detailProduct" : "deputyProductDetail") +"?productCode=" +
              productCode +
              "&productType=" +
              productType +
              "&spuCode=" +
              row.spuCode +
              "&skuCode=" +
              row.skuCode +
              "&applyCode=" +
              row.applyCode +
              "&dataType=" +
              row.dataType +
              "&from=productList" +
              "&detailType=self"+
              "&pageType=detail",
            row.prodType === "2" ? "商品详情" : "副商品详情"
          );
        } catch {
          this.$router.push({
            path: row.prodType === "2" ? "/product/detailProduct" : "/product/deputyProductDetail",
            query: {
              productCode: productCode,
              productType: productType,
              spuCode: row.spuCode,
              skuCode: row.skuCode,
              applyCode: row.applyCode,
              dataType: row.dataType,
              detailType: "self",
              pageType:"detail",
              from: "productList",
            },
          });
        }
      }
    },
    // 打开导出商品弹框
    handleExport() {
      this.dialogVisible = true;
      let settingss = {
        data: {
          key: {
            name: "title", //zTree 节点数据保存节点名称的属性名称  默认值："name"
          },
        },
        check: {
          chkboxType: { Y: "ps", N: "ps" },
          enable: true, //true 、 false 分别表示 显示 、不显示 复选框或单选框
          nocheckInherit: true, //当父节点设置 nocheck = true 时，设置子节点是否自动继承 nocheck = true
        },
      };
      this.$nextTick(() => {
        $.fn.zTree.init($("#dictTree"), settingss, exportProductDictList);
      });
    },
    // 清空导出字典选中状态
    clearTree() {
      let treeObj = $.fn.zTree.getZTreeObj("dictTree");
      treeObj.checkAllNodes(false);
    },
    // 导出商品列表
    sure() {
      let zTreeOjb = $.fn.zTree.getZTreeObj("dictTree");
      let arr = zTreeOjb.getCheckedNodes(true).filter((item) => {
        return item.parentId != 0;
      });
      if (arr.length >= 30) {
        this.$message.warning(
          "勾选字段超出30个，导出速度较慢，请先修改导出数量"
        );
      }
      let obj = { exportList: [], ...this.formData };
      arr.forEach((item) => {
        obj.exportList.push(item.title);
      });
      if (obj.updateTime && obj.updateTime.length > 0) {
        obj.updateStartTime = obj.updateTime[0];
        obj.updateEndTime = obj.updateTime[1];
      }
      if (obj.createTime && obj.createTime.length > 0) {
        obj.createStartTime = obj.createTime[0];
        obj.createEndTime = obj.createTime[1];
      }
      if (this.businessScopeList.length > 0) {
        obj.businessScopeMulti = this.businessScopeList[
          this.businessScopeList.length - 1
        ];
      }
      delete obj.updateTime;
      delete obj.createTime;
      exportProductList(obj).then((res) => {
        if (!res.retCode) {
          this.dialogVisible = false;
          this.$message.success(res.retMsg);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 同步下游
    async downstream(e) {
      try {
        const res = await downstream(e)
        if (res.retCode === 0) {
          this.$message.success(res.retMsg)
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
    /deep/ .vxe-table .vxe-body--row.row--current{
      background: #F5F7FA;
      color: #606266;
    }
  }
}
.btn {
  padding: 0 10px;
}
.btn-wrap /deep/ {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.tree-wrap /deep/ {
  padding-bottom: 20px;
  ul {
    li {
      line-height: 32px;
      span {
        font-size: 14px !important;
      }
      a {
        height: 32px;
        span {
          display: inline-block;
          vertical-align: middle !important;
          font-size: 14px !important;
          &.node_name {
            line-height: 32px;
          }
        }
      }
    }
  }
}
.tree-btn {
  border-bottom: 1px solid #dbdbdb;
  padding-bottom: 10px;
  span {
    color: #f56c6c;
  }
}
</style>
