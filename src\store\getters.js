const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  // 用户权限
  roles: state => state.permission.routes,
  permission_routes: state => state.permission.routes,
  // 商品问号内容
  info:state => state.product.info,
  permissionList: state=> state.user.permissionList,
  // 常用选择框选项数据
  // 商品分类
  // spuCategoryOptions
  // // 商品大类
  // largeCategoryOptions
  // // 所属经营范围
  // businessScopeListOptions
  // // 存储条件
  // storageCondOptions
  // // 一级分类
  // firstCategoryOptions
  // // 包装单位
  // packageUnitOptions
  // // 特殊属性
  // specialAttrListOptions
  // // 剂型
  // dosageFormOptions
  // // 处方分类
  // prescriptionCategoryOptions
  // // 税率
  // inRateOptions:
  // outRateOptions:
  // 生产厂家初始数据
  // distManufacturerOptions
  selectOptions:state=>state.product.selectOptions,
  // 商品分类对象
    // type:"EMPTY",
    // name:"",
    // id:""
  spuCategory:state=>state.product.spuCategory,
  spuApprovalNo:state=>state.product.spuApprovalNo,
  spuBusinessScopeListType:state=>state.product.spuBusinessScopeListType,
  generalName: state=>state.product.generalName,
  spec: state=>state.product.spec,
  isChangeCategory: state=>state.product.isChangeCategory,
  netContent: state=>state.product.netContent,
  netContentUnit: state=>state.product.netContentUnit,
  // 商品操作类型
  /**
   * 操作类型
   * 新增 :add
   * 草稿：draft
   * 详情 :detail
   * 修改："edit"
   * 一审：auditLevel1
   * 二审：auditLevel2
   * 复用：reuse
   * 同步：update
   */
  operationType:state=>state.product.operationType,
  // 是否展示自动填装包装规格
  unitTargetFirstCategory:state=>state.product.unitTargetFirstCategory,
  // 是否需要按照清洗规则，将字段拆分并填入到【净含量】 和 【净含量单位】两个字段上
  unitZSYYFirstCategory:state=>state.product.unitZSYYFirstCategory,
}
export default getters
