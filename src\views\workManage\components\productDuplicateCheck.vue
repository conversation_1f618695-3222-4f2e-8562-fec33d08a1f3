<template>
  <div>
    <!-- 标品查重弹窗 -->
    <el-dialog ref="productDuplicateCheck" :visible.sync="dialogVisible" :title="title" :width="width"
      :close-on-click-modal="false" :close-on-press-escape="false" class="product-duplicate-dialog">
      <div v-if="duplicateData && duplicateData.length > 0">
        <!-- 弹窗顶部导航 -->
        <div class="dialog-header">
          <div class="header-info">
            <span class="product-id">标品ID: {{ currentDuplicate.productId || '暂无' }}</span>
            <span class="position-info">{{ currentIndex + 1 }}/{{ duplicateData.length }}</span>
          </div>
          <div class="header-actions">
            <el-button size="small" type="danger" @click="openRejectDialog">
              标品重复驳回
            </el-button>
          </div>
        </div>

        <!-- 弹窗主体内容 -->
        <div class="dialog-content">
          <el-row :gutter="20">
            <!-- 左侧：新品提报信息 -->
            <el-col :span="12">
              <div class="content-section">
                <h3 class="section-title">新品提报信息</h3>
                <div class="product-info">
                  <!-- 商品信息字段 -->
                  <div class="field-list">
                    <div v-for="field in displayFields" :key="field.key" class="field-item"
                      :class="{ 'field-different': isFieldDifferent(field.key) }">
                      <span class="field-label">{{ field.label }}：</span>
                      <div class="field-value-container">
                        <span class="field-value">{{ getNewProductFieldValue(field.key) || '暂无' }}</span>
                        <el-button v-if="getNewProductFieldValue(field.key)" size="mini" type="text" class="copy-btn"
                          @click="copyInfo(getNewProductFieldValue(field.key))">
                          复制
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 商品图片 -->
                  <div class="image-section">
                    <!-- 调试信息 -->
                    <div v-if="true"
                      style="font-size: 12px; color: #999; margin-bottom: 10px; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                      <strong>🔍 图片调试信息:</strong>
                      <br>📸 newProductMainImage = {{ newProductMainImage || '(空值)' }}
                      <br>📦 外包装图片数量: {{ (skuData && skuData.outPackageImgList && skuData.outPackageImgList.length) || 0
                      }}
                      <br>📄 说明书图片数量: {{ (skuData && skuData.directionImgList && skuData.directionImgList.length) || 0
                      }}
                      <br>🎯 allImagesList数量: {{ (skuData && skuData.allImagesList && skuData.allImagesList.length) || 0
                      }}
                      <br>🏷️ standardProductMainImage = {{ standardProductMainImage || '(空值)' }}
                      <br>📋 当前标品索引: {{ currentIndex }} / {{ duplicateData.length }}
                      <div v-if="currentDuplicate" style="margin-top: 5px;">
                        <strong>当前标品属性:</strong>
                        <br>{{ Object.keys(currentDuplicate || {}).join(', ') }}
                        <br><strong>imageList:</strong> {{ currentDuplicate.imageList[0] || '(空值)' }}
                      </div>
                      <button @click="debugImageData" style="margin-top: 5px; padding: 2px 8px; font-size: 11px;">
                        🔍 手动调试图片数据
                      </button>
                      <button @click="testStandardImagePreview"
                        style="margin-top: 5px; margin-left: 5px; padding: 2px 8px; font-size: 11px;">
                        🖼️ 测试标品图片预览
                      </button>
                      <button @click="testComparePreview"
                        style="margin-top: 5px; margin-left: 5px; padding: 2px 8px; font-size: 11px;">
                        🔄 测试图片对比预览
                      </button>
                      <button @click="testRotationFeature"
                        style="margin-top: 5px; margin-left: 5px; padding: 2px 8px; font-size: 11px;">
                        🔄 测试旋转功能
                      </button>
                      <button @click="testControlButtons"
                        style="margin-top: 5px; margin-left: 5px; padding: 2px 8px; font-size: 11px;">
                        🎛️ 测试控制按钮
                      </button>
                    </div>

                    <div class="image-container">
                      <img v-if="newProductMainImage" :src="newProductMainImage" alt="新品图片" class="product-image"
                        @click="openComparePreview" />
                      <div v-else class="no-image">暂无图片</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <!-- 右侧：匹配标品信息 -->
            <el-col :span="12">
              <div class="content-section">
                <h3 class="section-title">
                  匹配标品信息
                  <el-button size="mini" type="primary" @click="viewStandardProductDetail" style="margin-left: 10px;">
                    查看标品详情
                  </el-button>
                </h3>
                <div class="product-info">
                  <!-- 标品信息字段 -->
                  <div class="field-list">
                    <div v-for="field in displayFields" :key="field.key" class="field-item"
                      :class="{ 'field-different': isFieldDifferent(field.key) }">
                      <span class="field-label">{{ field.label }}：</span>
                      <div class="field-value-container">
                        <span class="field-value">{{ getStandardProductFieldValue(field.key) || '暂无' }}</span>
                        <el-button v-if="getStandardProductFieldValue(field.key)" size="mini" type="text"
                          class="copy-btn" @click="copyInfo(getStandardProductFieldValue(field.key))">
                          复制
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 标品图片 -->
                  <div class="image-section">
                    <div class="image-container">
                      <img v-if="standardProductMainImage" :src="standardProductMainImage" alt="标品图片"
                        class="product-image" @click="openComparePreview" />
                      <div v-else class="no-image">暂无图片</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <span slot="footer" class="dialog-footer">
          <div class="footer-navigation">
            <el-button :disabled="currentIndex === 0" @click="previousDuplicate">
              上一条
            </el-button>
            <el-button :disabled="currentIndex === duplicateData.length - 1" @click="nextDuplicate">
              下一条
            </el-button>
            <!-- <el-button @click="close">关闭</el-button> -->
          </div>
        </span>
      </div>
      <div class="no-data-container" v-else>
        <div class="no-data-content">
          <h2>未查询到重复标品</h2>
        </div>
        <div class="no-data-actions">
          <el-button type="primary" @click="close">关闭</el-button>
        </div>
      </div>

    </el-dialog>

    <!-- 原有的图片预览组件 -->
    <imagePreview :on-close="closeImageViewer" v-if="imgPreview" :url-list="previewImageList"
      :initial-index="previewInitialIndex"></imagePreview>

    <!-- 新的图片对比预览组件 -->
    <imageComparePreview v-if="comparePreview" :new-product-images="newProductImagesForCompare"
      :standard-product-images="standardProductImagesForCompare" :on-close="closeComparePreview"></imageComparePreview>


  </div>
</template>

<script>
import imagePreview from "@/components/common/preview/imagePreview";
import imageComparePreview from "@/components/common/preview/imageComparePreview";

export default {
  name: 'productDuplicateCheck',
  components: {
    imagePreview,
    imageComparePreview
  },
  data() {
    return {
      dialogVisible: false,
      title: '标品查重',

      // 数据源
      spuData: {},
      skuData: {},
      duplicateData: [],
      currentIndex: 0,

      // 图片预览相关
      imgPreview: false,
      previewImageList: [],
      previewInitialIndex: 0,

      // 图片对比预览相关
      comparePreview: false,
      newProductImagesForCompare: [],
      standardProductImagesForCompare: [],

      // 固定展示字段
      fixedFields: [
        { key: 'skuName', label: '商品名称' },
        { key: 'generalName', label: '通用名' },
        { key: 'approvalNo', label: '批准文号' },
        { key: 'manufacturerName', label: '生产厂家' },
        { key: 'spec', label: '包装规格' },
        { key: 'smallPackageCode', label: '小包装条码' },
        { key: 'brand', label: '品牌' },
        { key: 'entrustedManufacturerName', label: '受托生产厂家' }
      ],

      // 动态字段映射
      dynamicFieldsMap: {
        1: [ // 普通药品
          { key: 'marketAuthor', label: '上市许可持有人' },
          { key: 'prescriptionCategoryName', label: '处方分类' }
        ],
        2: [ // 中药
          { key: 'originPlace', label: '产地' }
        ],
        3: [ // 医疗器械
          { key: 'filingsAuthor', label: '医疗器械注册人/备案人名称' }
        ],
        4: [ // 非药
          { key: 'filingsAuthor', label: '化妆品备案人/注册' }
        ]
      },
      width: '90%',
    }
  },
  computed: {
    // 当前匹配的标品数据
    currentDuplicate() {
      return this.duplicateData[this.currentIndex] || {};
    },

    // 显示的字段列表（固定字段 + 动态字段）
    displayFields() {
      const dynamicFields = this.dynamicFieldsMap[this.spuData.spuCategory] || [];
      return [...this.fixedFields, ...dynamicFields];
    },

    // 新品主图
    newProductMainImage() {
      console.log('=== newProductMainImage 计算属性调试 ===');
      console.log('this.skuData:', this.skuData);
      console.log('allImagesList:', this.skuData && this.skuData.allImagesList);
      console.log('外包装图片列表:', this.skuData && this.skuData.outPackageImgList);
      console.log('说明书图片列表:', this.skuData && this.skuData.directionImgList);

      // 获取图片URL的辅助函数
      const getImageUrl = (imageObj, index = 0) => {
        console.log(`=== getImageUrl 调试 (图片${index}) ===`);
        console.log('图片对象:', imageObj);

        if (!imageObj) {
          console.log('图片对象为空，返回 null');
          return null;
        }

        // 检查所有可能的URL字段
        const urlFields = ['mediaUrl', 'pictureUrl', 'url', 'src', 'path', 'imageUrl', 'imgUrl', 'fileUrl', 'filePath'];
        console.log('图片对象的所有属性:', Object.keys(imageObj));

        for (let field of urlFields) {
          if (imageObj[field]) {
            console.log(`找到URL字段 ${field}:`, imageObj[field]);
            return imageObj[field];
          }
        }

        console.log('未找到任何有效的URL字段');
        return null;
      };

      // 优先使用 allImagesList（包含所有图片：主图 + 外包装图片 + 说明书图片）
      if (this.skuData && this.skuData.allImagesList && Array.isArray(this.skuData.allImagesList) && this.skuData.allImagesList.length > 0) {
        console.log('使用 allImagesList，图片数量:', this.skuData.allImagesList.length);
        console.log('allImagesList 完整数组:', this.skuData.allImagesList);

        const firstImage = this.skuData.allImagesList[0];
        console.log('第一张图片对象:', firstImage);

        const imageUrl = getImageUrl(firstImage, 0);
        console.log('提取到的图片URL:', imageUrl);

        if (imageUrl) {
          console.log('✅ 使用 allImagesList 中的第一张图片:', imageUrl);
          return imageUrl;
        } else {
          console.log('❌ allImagesList 中的第一张图片URL为空');
        }
      }

      // 回退方案：优先显示外包装图片
      if (this.skuData && this.skuData.outPackageImgList && Array.isArray(this.skuData.outPackageImgList) && this.skuData.outPackageImgList.length > 0) {
        console.log('回退到外包装图片，数组长度:', this.skuData.outPackageImgList.length);
        console.log('外包装图片完整数组:', this.skuData.outPackageImgList);

        const firstImage = this.skuData.outPackageImgList[0];
        console.log('第一张外包装图片对象:', firstImage);

        const imageUrl = getImageUrl(firstImage, 0);
        console.log('提取到的外包装图片URL:', imageUrl);

        if (imageUrl) {
          console.log('✅ 使用外包装图片:', imageUrl);
          return imageUrl;
        } else {
          console.log('❌ 外包装图片URL为空');
        }
      }

      // 最后回退：如果没有外包装图片，则显示说明书图片
      if (this.skuData && this.skuData.directionImgList && Array.isArray(this.skuData.directionImgList) && this.skuData.directionImgList.length > 0) {
        console.log('回退到说明书图片，数组长度:', this.skuData.directionImgList.length);
        console.log('说明书图片完整数组:', this.skuData.directionImgList);

        const firstImage = this.skuData.directionImgList[0];
        console.log('第一张说明书图片对象:', firstImage);

        const imageUrl = getImageUrl(firstImage, 0);
        console.log('提取到的说明书图片URL:', imageUrl);

        if (imageUrl) {
          console.log('✅ 使用说明书图片:', imageUrl);
          return imageUrl;
        } else {
          console.log('❌ 说明书图片URL为空');
        }
      }

      console.log('❌ 没有找到任何有效图片，返回 null');
      return null;
    },

    // 标品主图
    standardProductMainImage() {
      const duplicate = this.currentDuplicate;

      // console.log('=== standardProductMainImage 计算属性调试 ===');
      // console.log('当前标品数据:', duplicate);
      // console.log('imageList:', duplicate && duplicate.imageList);

      // 直接使用 imageList 字段
      const imageUrl = duplicate && duplicate.imageList.length > 0 ? duplicate.imageList[0] : null;
      // console.log('返回的图片URL:', imageUrl);

      return imageUrl;
    }
  },
  methods: {
    // 打开弹窗
    open(spuData, skuData, duplicateData) {
      console.log('=== 标品查重弹窗打开 ===');
      console.log('SPU数据:', spuData);
      console.log('SKU数据:', skuData);
      console.log('重复数据:', duplicateData);

      // 详细检查图片数据
      console.log('=== 图片数据详细检查 ===');
      console.log('SKU数据中的图片字段:');
      console.log('- outPackageImgList:', skuData && skuData.outPackageImgList);
      console.log('- directionImgList:', skuData && skuData.directionImgList);
      console.log('- allImagesList:', skuData && skuData.allImagesList);
      console.log('- mainImageList:', skuData && skuData.mainImageList);
      console.log('- packageImageList:', skuData && skuData.packageImageList);
      console.log('- combinedPackageList:', skuData && skuData.combinedPackageList);

      // 验证数据修复效果
      console.log('=== 数据修复验证 ===');
      console.log('图片数量统计:', {
        outPackageImgList: skuData && skuData.outPackageImgList ? skuData.outPackageImgList.length : 0,
        directionImgList: skuData && skuData.directionImgList ? skuData.directionImgList.length : 0,
        allImagesList: skuData && skuData.allImagesList ? skuData.allImagesList.length : 0,
        mainImageList: skuData && skuData.mainImageList ? skuData.mainImageList.length : 0,
        packageImageList: skuData && skuData.packageImageList ? skuData.packageImageList.length : 0,
        combinedPackageList: skuData && skuData.combinedPackageList ? skuData.combinedPackageList.length : 0
      });

      if (skuData && skuData.outPackageImgList) {
        console.log('外包装图片数量:', skuData.outPackageImgList.length);
        if (skuData.outPackageImgList.length > 0) {
          console.log('外包装图片详情:');
          skuData.outPackageImgList.forEach((img, index) => {
            console.log(`外包装图片 ${index}:`, {
              name: img.name,
              mediaUrl: img.mediaUrl,
              url: img.url,
              pictureUrl: img.pictureUrl
            });
          });
        } else {
          console.warn('⚠️ outPackageImgList 为空数组！');
        }
      } else {
        console.warn('⚠️ outPackageImgList 不存在！');
      }

      if (skuData && skuData.directionImgList) {
        console.log('说明书图片数量:', skuData.directionImgList.length);
        if (skuData.directionImgList.length > 0) {
          console.log('说明书图片详情:');
          skuData.directionImgList.forEach((img, index) => {
            console.log(`说明书图片 ${index}:`, {
              name: img.name,
              mediaUrl: img.mediaUrl,
              url: img.url,
              pictureUrl: img.pictureUrl
            });
          });
        }
      }

      if (skuData && skuData.allImagesList) {
        console.log('所有图片数量:', skuData.allImagesList.length);
        if (skuData.allImagesList.length > 0) {
          console.log('所有图片详情:');
          skuData.allImagesList.forEach((img, index) => {
            console.log(`图片 ${index}:`, {
              name: img.name,
              mediaUrl: img.mediaUrl,
              url: img.url,
              pictureUrl: img.pictureUrl
            });
          });
        }
      }

      this.dialogVisible = true;
      this.spuData = spuData || {};
      this.skuData = skuData || {};
      this.duplicateData = duplicateData || [];
      this.currentIndex = 0;
      if (this.duplicateData && this.duplicateData.length > 0) {
        this.width = '90%'
      } else {
        this.width = '30%'
      }

      // 按匹配分数排序（假设有score字段，从高到低）
      this.duplicateData.sort((a, b) => (b.score || 0) - (a.score || 0));

      console.log('标品查重数据设置完成');

      // 调试图片数据
      // console.log('=== 图片数据调试 ===');
      // console.log('skuData完整对象:', skuData);
      // console.log('外包装图片 outPackageImgList:', skuData && skuData.outPackageImgList);
      // console.log('说明书图片 directionImgList:', skuData && skuData.directionImgList);

      // 检查所有可能的图片字段
      const imageFields = ['outPackageImgList', 'directionImgList', 'imageList', 'imgList', 'pictures', 'images'];
      imageFields.forEach(field => {
        if (skuData && skuData[field]) {
          // console.log(`发现图片字段 ${field}:`, skuData[field]);
        }
      });

      // 检查图片数据结构
      if (skuData && skuData.outPackageImgList && skuData.outPackageImgList.length > 0) {
        // console.log('外包装图片第一张结构:', skuData.outPackageImgList[0]);
        // console.log('外包装图片URL字段检查:', {
        //   pictureUrl: skuData.outPackageImgList[0].pictureUrl,
        //   url: skuData.outPackageImgList[0].url,
        //   src: skuData.outPackageImgList[0].src,
        //   path: skuData.outPackageImgList[0].path
        // });
      }
      if (skuData && skuData.directionImgList && skuData.directionImgList.length > 0) {
        // console.log('说明书图片第一张结构:', skuData.directionImgList[0]);
        // console.log('说明书图片URL字段检查:', {
        //   pictureUrl: skuData.directionImgList[0].pictureUrl,
        //   url: skuData.directionImgList[0].url,
        //   src: skuData.directionImgList[0].src,
        //   path: skuData.directionImgList[0].path
        // });
      }

      // 检查 skuData 的所有属性
      // console.log('skuData 所有属性:', Object.keys(skuData || {}));

      // console.log('=== 图片数据调试结束 ===');

      // 手动触发图片检查
      // this.debugImageData();

      // 添加键盘事件监听器用于测试ESC键处理
      // this.addKeyboardEventListener();
    },

    // 手动调试图片数据的方法
    debugImageData() {
      console.log('=== 手动图片数据调试 ===');
      console.log('完整的 skuData:', this.skuData);
      console.log('skuData 的类型:', typeof this.skuData);
      console.log('skuData 是否为数组:', Array.isArray(this.skuData));

      if (this.skuData) {
        console.log('skuData 的所有属性:', Object.keys(this.skuData));

        // 检查各种图片字段
        const imageFields = ['outPackageImgList', 'directionImgList', 'allImagesList', 'mainImageList', 'packageImageList'];
        imageFields.forEach(field => {
          const value = this.skuData[field];
          console.log(`${field}:`, value);
          console.log(`${field} 类型:`, typeof value);
          console.log(`${field} 是否为数组:`, Array.isArray(value));

          // 检查是否是响应式对象
          if (value && value.__ob__) {
            console.log(`${field} 是响应式对象，尝试获取原始数据`);
            console.log(`${field} 原始数据:`, JSON.parse(JSON.stringify(value)));
          }

          if (Array.isArray(value)) {
            console.log(`${field} 长度:`, value.length);
            if (value.length > 0) {
              console.log(`${field} 第一个元素:`, value[0]);
              console.log(`${field} 第一个元素的属性:`, Object.keys(value[0] || {}));

              // 检查第一个元素的URL字段
              const firstElement = value[0];
              if (firstElement) {
                const urlFields = ['mediaUrl', 'pictureUrl', 'url', 'src', 'path', 'imageUrl', 'imgUrl', 'fileUrl', 'filePath'];
                urlFields.forEach(urlField => {
                  if (firstElement[urlField]) {
                    console.log(`${field}[0].${urlField}:`, firstElement[urlField]);
                  }
                });
              }
            }
          }
        });
      }

      // 检查计算属性
      console.log('newProductMainImage 计算属性结果:', this.newProductMainImage);

      // 强制触发计算属性重新计算
      this.$forceUpdate();
      console.log('已强制更新组件');
    },

    // 测试标品图片预览功能
    testStandardImagePreview() {
      console.log('=== 测试标品图片预览功能 ===');
      console.log('当前状态:', {
        currentIndex: this.currentIndex,
        duplicateData: this.duplicateData,
        currentDuplicate: this.currentDuplicate,
        imgPreview: this.imgPreview
      });

      // 如果没有真实数据，创建测试数据
      if (!this.currentDuplicate || !this.currentDuplicate.imageList.length > 0) {
        console.log('没有真实数据，创建测试数据');
        const testImageUrl = 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png';

        // 构造测试图片对象
        const imageObj = {
          mediaUrl: testImageUrl,
          pictureUrl: testImageUrl,
          url: testImageUrl,
          name: '测试标品图片',
          uid: 'test_standard_product_image'
        };

        console.log('构造的测试图片对象:', imageObj);

        this.previewImageList = [imageObj];
        this.previewInitialIndex = 0;
        this.imgPreview = true;

        console.log('设置测试预览状态:', {
          previewImageList: this.previewImageList,
          previewInitialIndex: this.previewInitialIndex,
          imgPreview: this.imgPreview
        });

        this.$message.success('已打开测试图片预览');
      } else {
        // 手动调用预览方法
        this.previewStandardProductImages();
      }
    },

    // 添加键盘事件监听器用于测试
    addKeyboardEventListener() {
      this.keyboardHandler = (e) => {
        if (e.keyCode === 27) { // ESC键
          console.log('🔍 标品查重弹窗检测到ESC键事件:', e);
          console.log('🔍 图片预览状态:', this.imgPreview);
          if (this.imgPreview) {
            console.log('✅ 图片预览打开中，ESC键应该只关闭图片预览');
          } else {
            console.log('⚠️ 图片预览未打开，ESC键可能会关闭主弹窗');
          }
        }
      };

      document.addEventListener('keydown', this.keyboardHandler);
    },

    // 移除键盘事件监听器
    removeKeyboardEventListener() {
      if (this.keyboardHandler) {
        document.removeEventListener('keydown', this.keyboardHandler);
        this.keyboardHandler = null;
      }
    },

    // 关闭弹窗
    close() {
      this.dialogVisible = false;
      this.resetData();
      this.removeKeyboardEventListener();
    },

    // 重置数据
    resetData() {
      this.spuData = {};
      this.skuData = {};
      this.duplicateData = [];
      this.currentIndex = 0;
    },

    // 上一条
    previousDuplicate() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },

    // 下一条
    nextDuplicate() {
      if (this.currentIndex < this.duplicateData.length - 1) {
        this.currentIndex++;
      }
    },

    // 判断字段是否不同
    isFieldDifferent(fieldKey) {
      const newValue = this.getNewProductFieldValue(fieldKey);
      const standardValue = this.getStandardProductFieldValue(fieldKey);
      return newValue !== standardValue && (newValue || standardValue);
    },

    // 获取新品字段值
    getNewProductFieldValue(fieldKey) {
      // 处理小包装条码特殊情况
      if (fieldKey === 'smallPackageCode') {
        if (this.skuData.smallPackageCodeList && this.skuData.smallPackageCodeList.length > 0) {
          return this.skuData.smallPackageCodeList.join(', ');
        }
        return this.skuData.smallPackageCode || '';
      }

      // 处理厂家名称字段
      if (fieldKey === 'manufacturerName') {
        return this.skuData.manufacturerName || this.spuData.manufacturerName || this.spuData.manufacturer || '';
      }

      // 处理受托生产厂家字段
      if (fieldKey === 'entrustedManufacturerName') {
        return this.skuData.entrustedManufacturerName || this.spuData.entrustedManufacturerName || this.spuData.entrustedManufacturer || '';
      }

      // 先从skuData获取，再从spuData获取
      return this.skuData[fieldKey] || this.spuData[fieldKey] || '';
    },

    // 获取标品字段值
    getStandardProductFieldValue(fieldKey) {
      const duplicate = this.currentDuplicate;

      // 处理小包装条码特殊情况
      if (fieldKey === 'smallPackageCode') {
        if (duplicate.smallPackageCodeList && duplicate.smallPackageCodeList.length > 0) {
          return duplicate.smallPackageCodeList.join(', ');
        }
        return duplicate.smallPackageCode || '';
      }

      // 处理厂家名称字段
      if (fieldKey === 'manufacturerName') {
        return duplicate.manufacturerName || duplicate.manufacturer || '';
      }

      // 处理受托生产厂家字段
      if (fieldKey === 'entrustedManufacturerName') {
        return duplicate.entrustedManufacturerName || duplicate.entrustedManufacturer || '';
      }

      return duplicate[fieldKey] || '';
    },

    // 预览新品图片
    previewNewProductImages() {
      console.log('=== previewNewProductImages 方法调试 ===');
      console.log('this.skuData:', this.skuData);

      let imageList = [];

      // 优先使用 allImagesList
      if (this.skuData.allImagesList && Array.isArray(this.skuData.allImagesList) && this.skuData.allImagesList.length > 0) {
        console.log('使用 allImagesList，图片数量:', this.skuData.allImagesList.length);
        imageList = [...this.skuData.allImagesList];
      } else {
        // 回退方案：合并外包装图片和说明书图片
        if (this.skuData.outPackageImgList && Array.isArray(this.skuData.outPackageImgList) && this.skuData.outPackageImgList.length > 0) {
          console.log('添加外包装图片:', this.skuData.outPackageImgList.length, '张');
          imageList = [...this.skuData.outPackageImgList];
        }

        if (this.skuData.directionImgList && Array.isArray(this.skuData.directionImgList) && this.skuData.directionImgList.length > 0) {
          console.log('添加说明书图片:', this.skuData.directionImgList.length, '张');
          imageList = [...imageList, ...this.skuData.directionImgList];
        }
      }

      console.log('最终图片列表:', imageList);

      if (imageList.length > 0) {
        this.previewImageList = imageList;
        this.previewInitialIndex = 0;
        this.imgPreview = true;
        console.log('打开图片预览，图片数量:', imageList.length);
      } else {
        console.log('没有图片可预览');
        this.$message.info('暂无图片可预览');
      }
    },

    // 预览标品图片
    previewStandardProductImages() {
      // console.log('=== previewStandardProductImages 方法调试 ===');
      const duplicate = this.currentDuplicate;
      // console.log('当前标品数据:', duplicate);
      // console.log('imageList:', duplicate && duplicate.imageList);

      // 只预览主图片
      if (duplicate && duplicate.imageList.length > 0) {
        // 构造图片对象，兼容图片预览组件的格式
        duplicate.imageList.forEach((img, index) => {
          const imageObj = {
            mediaUrl: img,
            pictureUrl: img,
            url: img,
            name: '标品图片',
            uid: 'standard_product_image'
          };
          this.previewImageList.push(imageObj);
        })

        // console.log('构造的图片对象:', imageObj);

        // this.previewImageList = [imageObj];
        this.previewInitialIndex = 0;
        this.imgPreview = true;

        // console.log('设置预览状态:', {
        //   previewImageList: this.previewImageList,
        //   previewInitialIndex: this.previewInitialIndex,
        //   imgPreview: this.imgPreview
        // });

        // 使用nextTick确保DOM更新
        this.$nextTick(() => {
          // console.log('nextTick后的预览状态:', this.imgPreview);
        });
      } else {
        // console.log('❌ 没有图片可预览');
        this.$message.info('暂无图片可预览');
      }
    },

    // 关闭图片预览
    closeImageViewer() {
      this.imgPreview = false;
      this.previewImageList = [];
      this.previewInitialIndex = 0;
    },

    // 打开图片对比预览
    openComparePreview() {
      console.log('=== 打开图片对比预览 ===');
      // 
      // 准备新品图片数据
      this.prepareNewProductImagesForCompare();

      // 准备标品图片数据
      this.prepareStandardProductImagesForCompare();

      // console.log('新品图片数据:', this.newProductImagesForCompare);
      // console.log('标品图片数据:', this.standardProductImagesForCompare);

      // 打开对比预览
      this.comparePreview = true;
    },

    // 准备新品图片数据
    prepareNewProductImagesForCompare() {
      console.log('=== 准备新品图片数据 ===');
      const images = [];

      // 获取图片URL的辅助函数
      const getImageUrl = (imageObj) => {
        if (!imageObj) return null;

        const urlFields = ['mediaUrl', 'pictureUrl', 'url', 'src', 'path', 'imageUrl', 'imgUrl', 'fileUrl', 'filePath'];
        for (let field of urlFields) {
          if (imageObj[field]) {
            return imageObj[field];
          }
        }
        return null;
      };

      // 优先使用 allImagesList
      if (this.skuData && this.skuData.allImagesList && Array.isArray(this.skuData.allImagesList) && this.skuData.allImagesList.length > 0) {
        console.log('使用 allImagesList 准备图片数据，数量:', this.skuData.allImagesList.length);
        this.skuData.allImagesList.forEach((img, index) => {
          const url = getImageUrl(img);
          if (url) {
            images.push({
              url: url,
              name: `图片 ${index + 1}`,
              type: '商品图片',
              size: img.pictureWidth && img.pictureHeight ? `${img.pictureWidth}x${img.pictureHeight}` : null,
              originalData: img
            });
          }
        });
      } else {
        // 回退方案：分别添加外包装图片和说明书图片
        console.log('回退到分别处理外包装图片和说明书图片');

        // 添加外包装图片
        if (this.skuData && this.skuData.outPackageImgList && Array.isArray(this.skuData.outPackageImgList) && this.skuData.outPackageImgList.length > 0) {
          console.log('添加外包装图片:', this.skuData.outPackageImgList.length, '张');
          this.skuData.outPackageImgList.forEach((img, index) => {
            const url = getImageUrl(img);
            if (url) {
              images.push({
                url: url,
                name: `外包装图片 ${index + 1}`,
                type: '外包装图片',
                size: img.pictureWidth && img.pictureHeight ? `${img.pictureWidth}x${img.pictureHeight}` : null,
                originalData: img
              });
            }
          });
        }

        // 添加说明书图片
        if (this.skuData && this.skuData.directionImgList && Array.isArray(this.skuData.directionImgList) && this.skuData.directionImgList.length > 0) {
          console.log('添加说明书图片:', this.skuData.directionImgList.length, '张');
          this.skuData.directionImgList.forEach((img, index) => {
            const url = getImageUrl(img);
            if (url) {
              images.push({
                url: url,
                name: `说明书图片 ${index + 1}`,
                type: '说明书图片',
                size: img.pictureWidth && img.pictureHeight ? `${img.pictureWidth}x${img.pictureHeight}` : null,
                originalData: img
              });
            }
          });
        }
      }

      // 如果没有图片，添加占位符
      if (images.length === 0) {
        console.log('没有找到任何图片，添加占位符');
        images.push({
          url: null,
          name: '暂无图片',
          type: '无图片',
          size: null,
          originalData: null
        });
      }

      this.newProductImagesForCompare = images;
      console.log('处理后的新品图片:', images);
    },

    // 准备标品图片数据
    prepareStandardProductImagesForCompare() {
      const images = [];
      const duplicate = this.currentDuplicate;

      if (duplicate && duplicate.imageList.length > 0) {
        duplicate.imageList.forEach((img, index) => {
          const imageObj = {
            mediaUrl: img,
            pictureUrl: img,
            url: img,
            name: `标品图片 ${index + 1}`,
            type: '标品图片',
            size: null,
            originalData: img
          }
          images.push(imageObj);
        })
      }

      // 如果没有图片，添加占位符
      if (images.length === 0) {
        images.push({
          url: null,
          name: '暂无图片',
          type: '无图片',
          size: null,
          originalData: null
        });
      }

      this.standardProductImagesForCompare = images;
      // console.log('处理后的标品图片:', images);
    },

    // 关闭图片对比预览
    closeComparePreview() {
      // console.log('关闭图片对比预览');
      this.comparePreview = false;
      this.newProductImagesForCompare = [];
      this.standardProductImagesForCompare = [];
    },

    // 测试图片对比预览功能
    testComparePreview() {
      console.log('=== 测试图片对比预览功能 ===');

      // 创建测试数据
      const testNewProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试外包装图片 1',
          type: '外包装图片',
          size: '800x600'
        },
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试说明书图片 1',
          type: '说明书图片',
          size: '600x800'
        }
      ];

      const testStandardProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试标品主图',
          type: '标品主图',
          size: '800x600'
        }
      ];

      // 设置测试数据
      this.newProductImagesForCompare = testNewProductImages;
      this.standardProductImagesForCompare = testStandardProductImages;

      console.log('设置测试数据:', {
        newProductImages: this.newProductImagesForCompare,
        standardProductImages: this.standardProductImagesForCompare
      });

      // 打开对比预览
      this.comparePreview = true;

      this.$message.success('已打开测试图片对比预览');
    },

    // 测试旋转功能
    testRotationFeature() {
      console.log('=== 测试旋转功能 ===');

      // 创建测试数据，包含不同尺寸的图片用于测试旋转效果
      const testNewProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试外包装图片（正方形）',
          type: '外包装图片',
          size: '800x800'
        },
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试说明书图片（长方形）',
          type: '说明书图片',
          size: '600x900'
        }
      ];

      const testStandardProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试标品主图（宽图）',
          type: '标品主图',
          size: '1200x600'
        }
      ];

      // 设置测试数据
      this.newProductImagesForCompare = testNewProductImages;
      this.standardProductImagesForCompare = testStandardProductImages;

      console.log('设置旋转测试数据:', {
        newProductImages: this.newProductImagesForCompare,
        standardProductImages: this.standardProductImagesForCompare
      });

      // 打开对比预览
      this.comparePreview = true;

      this.$message.success('已打开旋转功能测试，请尝试使用旋转按钮或快捷键：Ctrl+方向键');
    },

    // 测试控制按钮显示
    testControlButtons() {
      console.log('=== 测试控制按钮显示 ===');

      // 创建简单的测试数据
      const testNewProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试图片 - 检查控制按钮',
          type: '测试图片',
          size: '800x600'
        }
      ];

      const testStandardProductImages = [
        {
          url: 'https://files.test.ybm100.com/BMP/product/d8d0d5e39d164c959f3fff489107b63e.png',
          name: '测试标品图片 - 检查控制按钮',
          type: '测试标品图片',
          size: '800x600'
        }
      ];

      // 设置测试数据
      this.newProductImagesForCompare = testNewProductImages;
      this.standardProductImagesForCompare = testStandardProductImages;

      console.log('设置控制按钮测试数据:', {
        newProductImages: this.newProductImagesForCompare,
        standardProductImages: this.standardProductImagesForCompare
      });

      // 打开对比预览
      this.comparePreview = true;

      this.$message({
        message: '已打开控制按钮测试！请检查：\n1. 左侧图片下方是否有红色边框的控制按钮区域\n2. 右侧图片下方是否有绿色边框的控制按钮区域\n3. 每个按钮是否有不同颜色的背景',
        type: 'success',
        duration: 8000,
        dangerouslyUseHTMLString: true
      });
    },

    // 查看标品详情
    viewStandardProductDetail() {
      const duplicate = this.currentDuplicate;

      // var productCode = duplicate.sauCode != duplicate.skuCode ? duplicate.sauCode : duplicate.skuCode;
      // var productType = duplicate.prodType == 2 ? 2 : 3;
      // console.log('====================================');
      // console.log(duplicate, 'jiangtaotao');
      // console.log(productCode, 'qiyu');
      // console.log(productType, 'lwq');
      // console.log('====================================');
      this.$emit('productDetail', duplicate)
    },

    // 打开驳回弹窗 - 通过事件通知父组件
    openRejectDialog() {
      const rejectData = {
        currentDuplicate: this.currentDuplicate,
        spuData: this.spuData,
        skuData: this.skuData,
        productId: this.currentDuplicate.productId || this.currentDuplicate.productCode || '',
        duplicateIndex: this.currentIndex
      };

      // console.log('发送驳回事件到父组件:', rejectData);
      this.$emit('reject-product', rejectData);
    },

    // 复制功能
    copyInfo(info) {
      if (info) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(info).then(() => {
            this.$message.success('复制成功');
          });
        } else {
          const textArea = document.createElement('textarea');
          textArea.value = info;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('复制成功');
        }
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.product-duplicate-dialog .el-dialog {
  margin-top: 5vh !important;
}

.product-duplicate-dialog .el-dialog__body {
  padding: 10px 20px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.product-id {
  font-weight: bold;
  color: #303133;
  font-size: 16px;
}

.position-info {
  background: #f0f2f5;
  padding: 4px 12px;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.dialog-content {
  min-height: 500px;
}

.content-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.section-title {
  background: #f5f7fa;
  margin: 0;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-info {
  padding: 16px;
}

.image-section {
  margin-top: 20px;
  text-align: center;
}

.image-container {
  width: 120px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0 auto;
}

.image-container:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-image {
  color: #c0c4cc;
  font-size: 14px;
  text-align: center;
}

.field-list {
  max-height: 350px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.field-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.field-item:last-child {
  border-bottom: none;
}

.field-item.field-different {
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  padding-left: 12px;
}

.field-label {
  min-width: 120px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.field-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.field-value {
  color: #303133;
  word-break: break-all;
  line-height: 1.4;
  flex: 1;
}

.field-different .field-value {
  color: #f56c6c;
  font-weight: 500;
}

.copy-btn {
  margin-left: 8px;
  padding: 2px 6px;
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.copy-btn:hover {
  opacity: 1;
}

.dialog-footer {
  text-align: center;
  padding: 15px 0;
}

.footer-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.no-data-container {
  .no-data-content {
    text-align: center;
    padding: 50px 0;
    font-size: 16px;
    color: rgba(250, 80, 80, 0.8);
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    margin-top: 20px;
    margin-bottom: 20px;
    background: #f5f7fa;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .no-data-actions {
    float: right;
  }
}

/* 驳回弹窗样式 */
.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.el-checkbox {
  margin-right: 0;
  margin-bottom: 10px;
}

.el-checkbox__label {
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .product-duplicate-dialog .el-dialog {
    width: 95% !important;
    margin-top: 2vh !important;
  }

  .dialog-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .dialog-content .el-col {
    margin-bottom: 20px;
  }

  .field-item {
    flex-direction: column;
    gap: 4px;
  }

  .field-label {
    min-width: auto;
    font-size: 13px;
  }

  .field-value {
    font-size: 13px;
    padding-left: 10px;
  }
}

/* 滚动条样式 */
.field-list::-webkit-scrollbar {
  width: 6px;
}

.field-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>