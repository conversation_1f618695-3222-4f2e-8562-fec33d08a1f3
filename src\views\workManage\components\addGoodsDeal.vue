<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs type="border-card">
      <el-tab-pane label="基础属性">
        <approval-process-new :approvalData="approvalData" :boardData="urlParam"></approval-process-new>
        <spu
          :formDisable="urlParam.modify == 1 ? false : true"
          ref="spu"
          :spuData="spuData"
          :skuData="skuData"
          :sauData="sauData"
          :changeList="productModifyField.spuFieldList"
          :coorection="coorection"
          :formAuth="btnAuth.editableTextBoxList"
        ></spu>
        <sku
          :formDisable="urlParam.modify == 1 ? false : true"
          ref="sku"
          :skuData="skuData"
          :sauData="sauData"
          :changeList="productModifyField.skuFieldList"
          :coorection="coorection"
          :formAuth="btnAuth.editableTextBoxList"
        ></sku>
        <image-review
          ref="imageReview"
          :outPackageImgList="getOutPackageImgList()"
          :directionImgList="getDirectionImgList()"
          :formDisable="true"
          :enableOCR="false"
          :enableDrag="false"
          @change="handleImageReviewChange"
        ></image-review>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false" :formDisable="urlParam.modify == 1 ? false : true"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'"
        :newAudit="true" modify="urlParam.modify" :changeList="productModifyField.extendFieldList"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'"
        :newAudit="true" modify="urlParam.modify" :changeList="productModifyField.extendFieldList"></extended-attr2>
        <modify-record :recordData="recordData"></modify-record>
      </el-tab-pane>
      <el-tab-pane label="资质属性">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane label="销售属性">
        <sales-attr></sales-attr>
      </el-tab-pane>
    </el-tabs>
    <el-row class="btns" :style="{ top: scrollTop < 40 ? '40px' : '0px' }">
      <!-- 一审和二审 -->
      <el-col
        :span="24"
        class="text-rt"
      >
        <!-- <el-button
          type="primary"
          @click="hang()"
          v-show="showHangBtn && operationType !== 'auditLevel3'"
          >挂起</el-button
        >
        <el-button type="primary" v-show="editState" @click="submit()"
          >修改</el-button
        >
        <el-button
          type="primary"
          v-show="
            editState && $store.getters.spuCategory.type !== 'GENERAL_MEDICINE'
          "
          @click="submit('checkSpu')"
          >spu唯一性校验</el-button
        >
        <el-button @click="review(true)" v-show="!editState"
          >审核通过</el-button
        >
        <el-button @click="review(false)">{{
          urlParam.approvalProcess == 6 ? "审核不通过" : "审核驳回"
        }}</el-button> -->
        <el-button type="info" @click="cancel"
          >取消提交</el-button
        >
        <el-button v-if="btnAuth.rejectToStart" type="warning" @click="showDialog('backToFirst')"
          >回退到提交人</el-button
        >
        <el-button v-if="btnAuth.rejectToReturn" type="warning" @click="showDialog('backToLast')"
          >回退到上一级审批人</el-button
        >
        <el-button v-if="btnAuth.reject" type="danger" @click="showDialog('stopTask')"
          >结束任务</el-button
        >
        <el-button v-if="btnAuth.agreed" type="success" @click="showDialog('pass')"
          >审核通过</el-button
        >
        <el-button v-if="urlParam.modify == 1" type="primary" @click="showDialog('reCommit')"
          >重新提交</el-button
        >
      </el-col>
      <!-- 预首营 -->
      <!-- <el-col :span="24" class="text-rt" v-if="operationType=='operate'">
        <el-button type="primary" @click="submit()">提交</el-button>
      </el-col>-->
      <!-- 驳回修改 -->
      <!-- <el-col :span="24" class="text-rt" v-if="operationType == 'RejectEdit'">
        <el-button @click="review(false, true)">结束流程</el-button>
        <el-button type="primary" @click="submit()">重新提交</el-button>
        <el-button
          type="primary"
          v-show="$store.getters.spuCategory.type !== 'GENERAL_MEDICINE'"
          @click="submit('checkSpu')"
          >spu唯一性校验</el-button
        >
      </el-col> -->
    </el-row>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      :width="showEnd ? '80%' : '500px'"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <p v-if="showEnd">商品已存在:</p>
        <el-radio-group v-if="showEnd" v-model="checkList" style="width:50%">
          <el-radio label="1">提报的商品已有标准库信息</el-radio>
          <el-input
          :disabled="checkList !== '1' ? true : false"
          style="width:200px;margin-left:40px"
          v-model="reviewForm.duplicateProductId"
          placeholder="请输入标准库ID"
        ></el-input>
        </el-radio-group>
        <p v-if="showEnd">新增错误:</p>
        <el-radio-group v-if="showEnd" v-model="checkList" style="width:50%">
          <el-radio label="2">新增商品信息错误</el-radio>
        </el-radio-group>
        <p v-if="showEnd">修改错误:</p>
        <el-radio-group v-if="showEnd" v-model="checkList" style="width:50%">
          <el-radio label="3">修改信息错误</el-radio>
        </el-radio-group>
        <el-form-item
          :label="showEnd ? '' : '审核意见'"
          prop="value"
        >
          <el-input
            :style="{'margin-top': showEnd ? '15px': 0 }"
            :disabled="banComment"
            type="textarea"
            :placeholder="showEnd ? '请输入自定义结束任务原因' : '审核意见'"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false;clearForm()">取 消</el-button>
        <el-button type="primary" @click="goSubmit()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getProductData, //获取商品信息
  productRejectModify, //驳回商品修改
  productRejectFinish, //审核不通过流程
  productModifyField, //获取商品修改变更字段
  checkSpuSingleApi, //检查SPU唯一性
} from "@/api/product";
import {
  review, // 审核商品
} from "@/api/worksubstitution";

import modifyRecord from "@/views/product/modifyRecord";
import qualificationAttr from "./qualificationAttr";
import salesAttr from "./salesAttr";
import { getAuth, getAddProcessData, auditRejectEnd, auditRejectStart, auditRejectPrev, auditPass, rejectModify } from "@/api/workManage"
import { isNumber } from "@/utils"
import imageReview from "@/views/product/imageReview.vue";

export default {
  name: "",
  mixins: [productMixinBase],
  components: { modifyRecord, qualificationAttr, salesAttr, imageReview },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    // 是否为实施提报商品审核
    isImplement() {
      if (
        this.approvalData.applyAttribute &&
        this.approvalData.applyAttribute.productSource
      ) {
        return this.approvalData.applyAttribute.productSource == "实施提报"
          ? true
          : false;
      } else {
        return false;
      }
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  data() {
    return {
      productLoading:false,
      btnAuth:{},
      operation: '',//弹框确认的操作
      coorection: {},
      recordData: [], // 修改记录
      scrollTop: 0,
      editState: false,
      dialogFormVisible: false,
      // 实时提报时，驳回原因选项
      rejectOptions: [
        {
          value: 3,
          label: "商品信息不完整，请实施修改后提报",
        },
        {
          value: 2,
          label: "商品无需匹配标准库",
        },
        {
          value: 4,
          label: "提报的商品已有标准库信息",
        },
      ],
      checkList: "",
      showEnd: false,
      banComment: false,
      reviewForm: {
        value: "",
        duplicateProductId: null,
        remarkCode: null,
      },
      rules: {
        value: [
          {
            required: true,
            message: "审核意见不能为空",
            trigger: "blur",
          },
        ],
      },
      productModifyField: {},
    };
  },
  watch: {
    checkList(e){
      if (e === "1") {
        this.reviewForm.value = ""
        this.banComment = true
      } else {
        this.banComment = false
        this.reviewForm.duplicateProductId = ""
      }
    }
  },
  created() {
    console.log(this.urlParam);
    if(this.urlParam.modify == 1) {
      this.productLoading = true
      this.$store.commit("product/SET_OPERATION_TYPE", "RejectEdit")
    }
    this.getBtnAuth()
    this.init();
    window.onscroll = () => {
      this.scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
    };
  },
  methods: {
    /**
     * 获取外包装图片列表
     */
    getOutPackageImgList() {
      if (this.skuData && this.skuData.length > 0) {
        return this.skuData[0].outPackageImgList || [];
      }
      return [];
    },

    /**
     * 获取说明书图片列表
     */
    getDirectionImgList() {
      if (this.skuData && this.skuData.length > 0) {
        return this.skuData[0].directionImgList || [];
      }
      return [];
    },

    /**
     * 处理图片审核组件数据变化
     */
    handleImageReviewChange(data) {
      console.log('newGoodsReportDeal: handleImageReviewChange called', {
        mainImageCount: data.mainImageList ? data.mainImageList.length : 0,
        packageImageCount: data.packageImageList ? data.packageImageList.length : 0,
        instructionImageCount: data.instructionImageList ? data.instructionImageList.length : 0,
        combinedPackageCount: data.combinedPackageList ? data.combinedPackageList.length : 0,
        useChannelImages: data.useChannelImages,
        imageQuality: data.imageQuality
      });

      // 更新 SKU 数据中的图片信息
      if (this.skuData && this.skuData.length > 0) {
        // 更新外包装图片（主图 + 外包装图片）
        const newOutPackageList = data.combinedPackageList || [];
        const newDirectionList = data.directionImgList || [];

        // 只有在数据真正变化时才更新
        const currentOutPackageList = this.skuData[0].outPackageImgList || [];
        const currentDirectionList = this.skuData[0].directionImgList || [];

        if (JSON.stringify(currentOutPackageList) !== JSON.stringify(newOutPackageList)) {
          console.log('newGoodsReportDeal: Updating outPackageImgList', {
            oldCount: currentOutPackageList.length,
            newCount: newOutPackageList.length
          });
          this.skuData[0].outPackageImgList = newOutPackageList;
        }

        if (JSON.stringify(currentDirectionList) !== JSON.stringify(newDirectionList)) {
          console.log('newGoodsReportDeal: Updating directionImgList', {
            oldCount: currentDirectionList.length,
            newCount: newDirectionList.length
          });
          this.skuData[0].directionImgList = newDirectionList;
        }
      }

      // 触发数据变化事件
      this.$bus.$emit("productChange", true);
    },

    clearForm() {
      this.showEnd = false
      this.checkList = ""
      this.rules.value[0].required = true
      this.reviewForm = {
        value: "",
        duplicateProductId: null,
        remarkCode: null,
      }
    },
    // 获取按钮权限
    async getBtnAuth(){
      console.log(this.urlParam)
      let param = {
        procDefId: this.urlParam.procDefId,
        nodeKey: this.urlParam.nodeKey
      }
      try {
        const { data } = await getAuth(param)
        console.log(data);
        this.btnAuth = data
      }
      catch(error) {
        console.log(error)
      }
    },
    showDialog(e) {
      if(e === 'pass' || e === 'reCommit') {
        this.rules.value[0].required = false
      } else {
        this.rules.value[0].required = true
      }
      if (e === "stopTask") {
        this.showEnd = true
        this.rules.value[0].required = false
      }
      this.dialogFormVisible = true
      this.operation = e
    },
    close() {
      let tab = 'second'
      parent.CreateTab(
        `../static/dist/index.html#/workManage/receiveDeal?tab=${tab}`,
        "领取与处理",
        true
      );
      this.cancel()
    },
    goSubmit() {
      this.$refs['reviewForm'].validate((valid) => {
        if (valid) {
          if(this.operation === 'pass') {
            this.pass()
          } else if(this.operation === 'backToFirst') {
            this.backToFirst()
          } else if(this.operation === 'backToLast') {
            this.backToLast()
          } else if(this.operation === 'stopTask') {
            this.stopTask()
          } else if(this.operation === 'reCommit') {
            console.log('reCommit')
            this.submit()
          }
        }
      });
    },
    // 审核通过
    async pass() {
      let { data } = await this.$refs.spu.getSpuData()
      console.log(data);
      let skuData = this.$refs.sku.getSkuData();
      if(skuData[0].smallPackageCodeList && skuData[0].smallPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个小包装条码！')
        return
      } else if(skuData[0].mediumPackageCodeList && skuData[0].mediumPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个中包装条码！')
        return
      } else if(skuData[0].piecePackageCodeList && skuData[0].piecePackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个件包装条码！')
        return
      }
      let { firstCategory, secondCategory, thirdCategory, fourthCategory, fiveCategory, sixCategory, inRate, outRate, taxCategoryCode, spuCode, marketAuthor, instructionSpec, businessScopeList, dosageForm, shadingAttr } = data
      let spu = await this.$refs.spu.getSpuData();
      if(!spu.data.taxCategoryCode) {
        this.$message.error('请输入税务分类编码');
        return
      }
      let param = {
        skuCode:skuData[0].skuCode,
        spuCode,
        applyCode:this.urlParam.applyCode,
        comment:this.reviewForm.value,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId,
        updateFieldMap:this.btnAuth.editableTextBoxList.length ? {
          prescriptionCategory:skuData[0].prescriptionCategory,
          packageUnit:skuData[0].packageUnit,
          storageCond:skuData[0].storageCond,
          validity:skuData[0].validity,
          qualityStandard:skuData[0].qualityStandard,
          firstCategory, secondCategory, thirdCategory, fourthCategory, fiveCategory, sixCategory, inRate, outRate, taxCategoryCode, marketAuthor, instructionSpec, businessScopeList:businessScopeList.join(), dosageForm, shadingAttr } : null
      }
      const res = await auditPass(param)
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      }
    },
    // 回退到提交人
    async backToFirst() {
      let param = {
        applyCode:this.urlParam.applyCode,
        comment:this.reviewForm.value,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectStart(param)
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      }
    },
    // 回退到上一级审批人
    async backToLast() {
      let param = {
        applyCode:this.urlParam.applyCode,
        comment:this.reviewForm.value,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectPrev(param)
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      }
    },
    // 结束任务
    async stopTask() {
      if (!this.checkList && !this.reviewForm.value) {
        this.$message.error("请选择结束原因")
        return
      } else if (this.checkList === "1" && !this.reviewForm.duplicateProductId) {
        this.$message.error("请输入重复数据标准库ID！")
        return
      } else if (this.checkList === "1" && !isNumber(this.reviewForm.duplicateProductId)) {
        this.$message.error("标准库ID录入错误！")
        return
      }
      let param = {
        updateFieldMap:{
          duplicateProductId: this.reviewForm.duplicateProductId || null,
          remarkCode: this.reviewForm.remarkCode === "1" ? 1 : 9,
        },
        applyCode:this.urlParam.applyCode,
        comment: this.checkList !== "1" ? this.reviewForm.value : `与标准库ID：${this.reviewForm.duplicateProductId}重复`,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectEnd(param)
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      }
    },
    cancel() {
      parent.CloseTab(
        "../static/dist/index.html#/workManage/addGoodsDeal"
      );
    },
    async init() {
      if (this.urlParam.type && this.urlParam.type == "coorection") {
        this.coorectionData = JSON.parse(
          localStorage.getItem("coorectionData")
        );
        let coorectionObj = {};
        for (let key of this.coorectionData.detailDtoList) {
          coorectionObj[key.correctKey] = key.correctValue;
        }
        this.coorection = coorectionObj;
      }
      // 获取审批流信息
      await this.getApplyInfo();
      // 获取审核信息数据
      this.getProductInfo();
      // 检测页面数据值修改变化
      this.$bus.$on("productChange", (res) => {
        this.editState = res;
      });
    },
    // 获取商品数据
    async getProductInfo() {
      let resModify = await productModifyField({
        applyCode: this.urlParam.applyCode,
      });
      this.productModifyField = resModify.data;
      let res = await getProductData({
        spuCode: this.urlParam.spuCode,
        productType:
          this.urlParam.approvalProcess != 0 ? this.urlParam.productType : 4,
        applyCode: this.urlParam.applyCode,
        productCode: this.urlParam.productCode,
        detailType: "self",
      });
      this.spuData = Object.freeze(res.data.spu);
      this.skuData = Object.freeze(res.data.sku);
      this.sauData = Object.freeze(res.data.sau);
      this.recordData = Object.freeze(res.data.record);
      this.productLoading = false
    },
    // 审核弹层保存按钮
    save() {
      this.$refs.reviewForm.validate(async (valid) => {
        // 审核通过或审核驳回(预首营审核6：审核不通过)
        if (
          this.reviewForm.state == "审核通过" ||
          this.reviewForm.state == "审核驳回" ||
          this.reviewForm.state == "审核不通过"
        ) {
          if (valid || this.reviewForm.state == "审核通过") {
            this.productLoading = true;
            let reviewOpinion = "";
            let reviewOpinionType = 0;
            // 如果是实施提报，匹配对应的选项值内容至reviewOpinion； isImplement：实施提报
            if (this.reviewForm.state !== "审核通过" && this.isImplement) {
              for (let item of this.rejectOptions) {
                if (item.value == this.reviewForm.rejectState) {
                  reviewOpinion = item.label;
                }
              }
              reviewOpinionType = this.reviewForm.rejectState;
            } else {
              reviewOpinion = this.reviewForm.value;
            }

            let res = await review({
              id: this.reviewInfo.id,
              rejectStatus: this.reviewForm.state == "审核通过" ? 1 : 0,
              reviewStatus: this.reviewInfo.reviewStatus,
              applyCode: this.reviewInfo.applyCode,
              approvalProcess: this.reviewInfo.approvalProcess,
              reviewOpinion,
              reviewOpinionType,
              applyUserScope: this.reviewInfo.applyUserScope,
            });
            this.productLoading = false;
            if (res.retCode != 0) {
              this.$message.error(res.retMsg);
            } else {
              this.close()
            }
          }
        }
        // 结束流程
        if (valid && this.reviewForm.state == "结束流程") {
          this.productLoading = true;
          let res = await productRejectFinish({
            id: this.reviewInfo.id,
            reviewStatus: this.reviewInfo.reviewStatus,
            applyCode: this.reviewInfo.applyCode,
            reviewOpinion: this.reviewForm.value,
            approvalProcess: this.reviewInfo.approvalProcess,
          });
          this.productLoading = false;

          if (res.retCode != 0) {
            this.$message.error(res.retMsg);
          } else {
            this.close()
          }
        }
      });
    },
    dialogClose() {
      this.$refs.reviewForm.resetFields();
    },
    /**
     * @description:商品数据提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(checkSpuSingle) {
      let spu = await this.$refs.spu.getSpuData();
      let skuData = this.$refs.sku.getSkuData();
      let sau = [];
      let extendData = {};
      if(skuData[0].smallPackageCodeList && skuData[0].smallPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个小包装条码！')
        return
      } else if(skuData[0].mediumPackageCodeList && skuData[0].mediumPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个中包装条码！')
        return
      } else if(skuData[0].piecePackageCodeList && skuData[0].piecePackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个件包装条码！')
        return
      }
      if (this.spuCategory.type == "GENERAL_MEDICINE" || this.spuCategory.type == "TRADITIONAL_MEDICINE") {
        extendData = this.$refs.extend.getExtendData();
      }
      if(spu.data.spuCategory == 5){
        sau = this.sauData;
      }
      if (!spu.state || !skuData || !extendData) {
        // 未通过 SPU表单校验
        console.log(spu.data)
        console.log(skuData)
        console.log(extendData)
        return;
      };
      let spuData = spu.data;
      if(!spuData.taxCategoryCode) {
        this.$message.error('请输入税务分类编码');
        return
      }
      if (checkSpuSingle) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);

        if (res.success) {
          this.$message.success("spu唯一性校验通过");
        } else {
          this.$message.error(res.retMsg);
        }
        this.productLoading = false;
        return false;
      }
      // formatSubmitData 函数在 productMixinBase.js中定义
      let submitData = this.formatSubmitData(
        _.cloneDeep(spuData),
        _.cloneDeep(skuData),
        _.cloneDeep(sau),
        _.cloneDeep(extendData)
      );
      //操作类型,
      // (0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改)

      submitData.operType = 2;

      submitData.prodType = this.urlParam.productType; //商品编码类型(1:spu,2:sku,3:sau)
      submitData.prodCode = this.urlParam.productCode; //商品编码
      submitData.applyCode = this.urlParam.applyCode
        ? this.urlParam.applyCode
        : ""; //单据编号
      this.productLoading = true;
      //驳回修改
      submitData.applyCode=this.urlParam.applyCode,
      submitData.comment=this.reviewForm.value,
      submitData.procInstId=this.urlParam.procInstId,
      submitData.procKey=this.urlParam.procKey,
      submitData.taskId=this.urlParam.taskId
      this.submitForReject(submitData);
    },
    /**
     * @description:审核修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForAudit(submitData) {
      delete submitData.operType;
      console.log(submitData)
      const res = await rejectModify(submitData)
      this.productLoading = false;
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      }
    },
    /**
     * @description:审核驳回修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForReject(submitData) {
      delete submitData.operType;
      let res = await productRejectModify(submitData);
      this.productLoading = false;
      if (res.success) {
        this.close()
      } else {
        this.$message.error(res.retMsg);
      }
    },
    async getApplyInfo() {
      let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      };
      let res = await getAddProcessData(param);
      console.log(res);
      if (res.success) {
        this.approvalData = res.data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  // padding-bottom: 70px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/ .el-tabs__content {
    padding-top: 72px;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    // z-index: 3100;
  }
}
</style>
