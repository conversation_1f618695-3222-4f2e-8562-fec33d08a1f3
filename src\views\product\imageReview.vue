<template>
  <div class="image-review-container">
    <!-- 图片审核标题区域 -->
    <div class="image-audit-section">
      <div class="audit-header" @click="toggleAuditSection">
        <span class="audit-title">图片审核</span>
        <el-tooltip content="此处为新品上报的图片审核流程，此处图片审核完，不会流转至原独立的图片待审核列表。请务必按照正常的图片审核要求进行审核。" placement="top">
          <i class="el-icon-question audit-help-icon" @click.stop></i>
        </el-tooltip>
        <i :class="auditSectionExpanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" class="audit-toggle-icon"></i>
      </div>

      <el-collapse-transition>
        <div v-show="auditSectionExpanded" class="audit-content">
          <!-- 主图区域 -->
          <div class="main-image-section image-section">
            <div class="image-title">
              主图
              <br /><span class="image-limit">(最多1张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="mainImageUpload" :fileList="mainImageList" :limit="1" :uploadUrl="uploadUrl"
                :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag" :dragGroup="'images'"
                :dragPutValidator="canPutToMain" :globalOcrManager="this" @change="handleMainImageChange"
                @dragChange="handleDragChange" :preview="true" class="main-drag-area" data-container="main" />
            </div>
          </div>

          <!-- 外包装图片区域 -->
          <div class="package-image-section image-section">
            <div class="image-title">
              外包装图片
              <br /><span class="image-limit">(最多60张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="packageImageUpload" :fileList="packageImageList" :limit="60"
                :uploadUrl="uploadUrl" :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag"
                :dragGroup="'images'" :globalOcrManager="this" @change="handlePackageImageChange"
                @dragChange="handleDragChange" :preview="true" class="package-drag-area" data-container="package" />
            </div>
          </div>

          <!-- 说明书图片区域 -->
          <div class="instruction-image-section image-section">
            <div class="image-title">
              说明书图片
              <br /><span class="image-limit">(最多60张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="instructionImageUpload" :fileList="instructionImageList" :limit="60"
                :uploadUrl="uploadUrl" :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag"
                :dragGroup="'images'" :globalOcrManager="this" @change="handleInstructionImageChange"
                @dragChange="handleDragChange" :preview="true" class="instruction-drag-area"
                data-container="instruction" />
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 渠道图片使用控制 -->
    <div class="channel-image-controls">
      <el-form label-width="230px">
        <el-form-item label="是否使用渠道上传图片">
          <el-radio-group v-model="useChannelImages" :disabled="formDisable">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="图片质量">
          <el-radio-group v-model="imageQuality" :disabled="formDisable">
            <el-radio label="needs_design">需设计精修</el-radio>
            <el-radio label="direct_use">可直接使用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import DraggableImageUpload from "./components/DraggableImageUpload.vue";

export default {
  name: "ImageReview",
  components: {
    DraggableImageUpload,
  },
  props: {
    // 外包装图片数据（用于初始化）
    outPackageImgList: {
      type: Array,
      default: () => [],
    },
    // 说明书图片数据（用于初始化）
    directionImgList: {
      type: Array,
      default: () => [],
    },
    // 是否禁用表单
    formDisable: {
      type: Boolean,
      default: false,
    },
    // 是否启用OCR功能
    enableOCR: {
      type: Boolean,
      default: true,
    },
    // 是否启用拖拽功能
    enableDrag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 图片审核区域展开状态
      auditSectionExpanded: true,

      // 图片数据
      mainImageList: [],      // 主图（限制1张）
      packageImageList: [],   // 外包装图片
      instructionImageList: [], // 说明书图片

      // 渠道图片控制
      useChannelImages: true,
      imageQuality: 'needs_design',

      // 内部操作标志位，防止重复初始化
      isInternalOperation: false,

      // 初始化标志位，防止重复初始化
      hasInitialized: false,

      // 手动添加图片标志位，区分手动添加和初始化数据
      hasManuallyAddedImages: false,

      // 上传配置
      uploadUrl: process.env.VUE_APP_BASE_API + "/api/file/upload/fileAndName",

      // 全局 OCR 数据管理 - 跨容器共享
      globalOcrResults: {},
    };
  },
  watch: {
    // 监听外包装图片数据变化，用于初始化
    outPackageImgList: {
      handler(newVal) {
        // 重置手动添加标志位，允许重新初始化
        if (newVal && newVal.length > 0) {
          this.hasManuallyAddedImages = false;
        }
        this.initializeImageData();
      },
      immediate: true,
      deep: true,
    },
    // 监听说明书图片数据变化
    directionImgList: {
      handler(newVal) {
        // 重置手动添加标志位，允许重新初始化
        if (newVal && newVal.length > 0) {
          this.hasManuallyAddedImages = false;
        }
        this.initializeImageData();
      },
      immediate: true,
      deep: true,
    },
    // 监听渠道图片使用状态变化
    useChannelImages: {
      handler(newVal, oldVal) {
        if (!this.isInternalOperation && newVal !== oldVal) {
          this.isInternalOperation = true;
          this.emitDataChange();
          this.$nextTick(() => {
            this.isInternalOperation = false;
          });
        }
      },
    },
    // 监听图片质量选择变化
    imageQuality: {
      handler(newVal, oldVal) {
        if (!this.isInternalOperation && newVal !== oldVal) {
          this.isInternalOperation = true;
          this.emitDataChange();
          this.$nextTick(() => {
            this.isInternalOperation = false;
          });
        }
      },
    },
  },
  mounted() {
    this.initializeImageData();

    // 延迟初始化 OCR 数据，确保所有组件都已挂载
    this.$nextTick(() => {
      setTimeout(() => {
        this.refreshAllContainersOcrData();
      }, 500);
    });
  },
  methods: {
    /**
     * 初始化图片数据
     * 将外包装图片第一张设置为主图，剩余的保留在外包装区域
     */
    initializeImageData() {
      // 防止在内部操作（如拖拽）时重复初始化
      if (this.isInternalOperation) {
        return;
      }

      // 标记为内部操作，防止触发其他监听器
      this.isInternalOperation = true;

      // 处理外包装图片
      if (this.outPackageImgList && this.outPackageImgList.length > 0) {
        // 第一张图片作为主图
        this.mainImageList = [this.formatImageData(this.outPackageImgList[0])];
        // 剩余图片保留在外包装区域
        this.packageImageList = this.outPackageImgList.slice(1).map(img => this.formatImageData(img));
      } else {
        // 只有在没有手动添加图片的情况下才清空
        if (!this.hasManuallyAddedImages) {
          this.mainImageList = [];
          this.packageImageList = [];
        }
      }

      // 处理说明书图片
      if (this.directionImgList && this.directionImgList.length > 0) {
        this.instructionImageList = this.directionImgList.map(img => this.formatImageData(img));
      } else {
        // 只有在没有手动添加图片的情况下才清空
        if (!this.hasManuallyAddedImages) {
          this.instructionImageList = [];
        }
      }

      // 标记已经初始化过
      this.hasInitialized = true;

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 格式化图片数据，确保符合 ImageUploadWithOCR 组件的要求
     */
    formatImageData(imageData) {
      return {
        uid: imageData.uid || imageData.mediaUrl || imageData.url,
        name: imageData.name || imageData.mediaName || `image-${Date.now()}`,
        mediaName: imageData.mediaName || imageData.name || `image-${Date.now()}`,
        mediaUrl: imageData.mediaUrl || imageData.url,
        url: imageData.url || imageData.mediaUrl,
        status: imageData.status || 'success',
        response: imageData.response,
      };
    },

    /**
     * 切换图片审核区域的展开/折叠状态
     */
    toggleAuditSection(event) {
      // 阻止事件冒泡，防止意外触发其他事件
      if (event) {
        event.stopPropagation();
      }

      // 设置内部操作标志位
      this.isInternalOperation = true;
      this.auditSectionExpanded = !this.auditSectionExpanded;

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 主图变化处理
     */
    handleMainImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.mainImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.mainImageUpload) {
            this.$refs.mainImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 外包装图片变化处理
     */
    handlePackageImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.packageImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.packageImageUpload) {
            this.$refs.packageImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 说明书图片变化处理
     */
    handleInstructionImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.instructionImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.instructionImageUpload) {
            this.$refs.instructionImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 向父组件发送数据变化事件
     */
    emitDataChange() {
      const data = {
        mainImageList: this.mainImageList,
        packageImageList: this.packageImageList,
        instructionImageList: this.instructionImageList,
        useChannelImages: this.useChannelImages,
        imageQuality: this.imageQuality,
      };

      this.$emit('change', data);
    },

    /**
     * 获取所有图片数据（供父组件调用）
     */
    getImageData() {
      return {
        mainImageList: this.mainImageList,
        packageImageList: this.packageImageList,
        instructionImageList: this.instructionImageList,
        useChannelImages: this.useChannelImages,
        imageQuality: this.imageQuality,
        // 合并后的外包装图片列表（主图 + 外包装图片）
        combinedPackageList: [...this.mainImageList, ...this.packageImageList],
        // 说明书图片列表
        directionImgList: this.instructionImageList,
      };
    },

    /**
     * 拖拽变化处理
     */
    handleDragChange() {
      // 设置内部操作标志位
      this.isInternalOperation = true;
      // 拖拽完成后触发数据变化事件
      this.$nextTick(() => {
        this.emitDataChange();
        this.isInternalOperation = false;
      });
    },

    /**
     * 判断是否可以拖拽到主图区域
     * 主图区域最多只能有一张图片
     */
    canPutToMain(to, from, dragEl, evt) {
      const canPut = this.mainImageList.length < 1;
      return canPut;
    },

    /**
     * 重置初始化状态，允许重新初始化数据
     * 可以从外部调用此方法来强制重新初始化
     */
    resetInitializationState() {
      this.hasInitialized = false;
      this.hasManuallyAddedImages = false;
      this.initializeImageData();
    },

    /**
     * 获取全局 OCR 数据
     */
    getGlobalOcrData(file) {
      const key = file.uid || file.mediaUrl || file.url;
      const data = this.globalOcrResults[key] || null;
      return data;
    },

    /**
     * 设置全局 OCR 数据
     */
    setGlobalOcrData(file, ocrData) {
      const key = file.uid || file.mediaUrl || file.url;

      this.$set(this.globalOcrResults, key, {
        loading: ocrData.loading,
        extracted: ocrData.extracted,
        fields: [...(ocrData.fields || [])]
      });

      // 通知所有组件刷新 OCR 数据
      this.$nextTick(() => {
        this.notifyOcrDataUpdate(file);
      });
    },

    /**
     * 删除全局 OCR 数据
     */
    removeGlobalOcrData(file) {
      const key = file.uid || file.mediaUrl || file.url;
      if (this.globalOcrResults[key]) {
        this.$delete(this.globalOcrResults, key);
      }
    },

    /**
     * 通知所有组件 OCR 数据已更新
     */
    notifyOcrDataUpdate(file) {

      // 通知所有 DraggableImageUpload 组件刷新指定文件的 OCR 数据
      const components = [
        this.$refs.mainImageUpload,
        this.$refs.packageImageUpload,
        this.$refs.instructionImageUpload
      ].filter(comp => comp);

      components.forEach(comp => {
        if (comp && typeof comp.refreshOcrDataForFile === 'function') {
          comp.refreshOcrDataForFile(file);
        }
      });
    },

    /**
     * 处理拖拽变化 - 管理 OCR 数据迁移
     */
    handleDragChange(evt) {

      // 如果是添加事件，强制刷新目标容器的 OCR 数据
      if (evt.added && evt.added.element) {
        const file = evt.added.element;

        // 延迟刷新，确保 DOM 更新完成
        this.$nextTick(() => {
          setTimeout(() => {
            this.notifyOcrDataUpdate(file);
            this.refreshAllContainersOcrData();
          }, 100);
        });
      }

      // 如果是移除事件，OCR 数据保留在全局存储中
      if (evt.removed && evt.removed.element) {
        const file = evt.removed.element;
      }
    },

    /**
     * 刷新所有容器的 OCR 数据
     */
    refreshAllContainersOcrData() {

      const components = [
        this.$refs.mainImageUpload,
        this.$refs.packageImageUpload,
        this.$refs.instructionImageUpload
      ].filter(comp => comp);

      components.forEach(comp => {
        if (comp && typeof comp.refreshAllOcrData === 'function') {
          comp.refreshAllOcrData();
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.image-review-container {
  padding: 20px;

  .image-audit-section {
    margin-bottom: 30px;

    .audit-header {
      display: flex;
      align-items: center;
      padding: 15px 0;
      cursor: pointer;
      border-bottom: 1px solid #e4e7ed;

      .audit-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-right: 8px;
      }

      .audit-help-icon {
        color: #909399;
        margin-right: 8px;
        cursor: help;

        &:hover {
          color: #409eff;
        }
      }

      .audit-toggle-icon {
        color: #909399;
        margin-left: auto;
        transition: transform 0.3s ease;

        &:hover {
          color: #409eff;
        }
      }
    }

    .audit-content {
      padding-top: 20px;

      .image-section {
        margin-bottom: 30px;

        .image-title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 15px;

          .image-limit {
            color: #bababa;
            font-weight: normal;
            font-size: 12px;
          }
        }

        .image-content {
          min-height: 120px;

          .main-drag-area,
          .package-drag-area,
          .instruction-drag-area {
            min-height: 120px;
            border: 2px dashed transparent;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
            }
          }

          .main-drag-area {
            &.sortable-ghost {
              border-color: #409eff;
              background-color: rgba(64, 158, 255, 0.1);
            }
          }

          .package-drag-area,
          .instruction-drag-area {
            &.sortable-ghost {
              border-color: #67c23a;
              background-color: rgba(103, 194, 58, 0.1);
            }
          }
        }
      }
    }
  }

  .channel-image-controls {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    /deep/ .el-form-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    /deep/ .el-form-item__label {
      font-weight: 600;
      color: #303133;
    }

    /deep/ .el-radio-group {
      .el-radio {
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

// 与 SKU 组件保持一致的样式
.image-box2 {
  display: flex;
  margin-bottom: 30px;

  .img-title {
    width: 120px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .img-content {
    flex: 1;
  }
}
</style>